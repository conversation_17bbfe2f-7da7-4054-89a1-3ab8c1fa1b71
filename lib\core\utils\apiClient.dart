import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:guest_mobile_app/core/constants/constants.dart';
import 'package:guest_mobile_app/core/utils/tokenManager.dart';
import 'package:guest_mobile_app/main.dart';

class ApiClient {
   final Dio dio = Dio(
    BaseOptions(
      baseUrl: AppConstants.apiBaseUrl, // Replace with your API URL
      connectTimeout: Duration(seconds: 10),
      receiveTimeout: Duration(seconds: 10),
    ),
  );

  ApiClient() {
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Attach the token to headers
        String? token = await TokenManager.getToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        return handler.next(options);
      },
      onResponse: (response, handler) {
        return handler.next(response);
      },
      onError: (DioError error, handler) async {
        if (error.response?.statusCode == 401) {
          // Token expired, clear token and redirect to login
          await TokenManager.clearToken();
          navigatorKey.currentState?.pushNamedAndRemoveUntil(
              '/login', (Route<dynamic> route) => false);
        }
        return handler.next(error);
      },
    ));
  }
}
