import 'package:dio/dio.dart';
import 'package:guest_mobile_app/core/utils/apiClient.dart';
import 'package:guest_mobile_app/core/utils/tokenManager.dart';

class AuthService {
  static const String isLoggedInKey = 'isLoggedIn';
  final ApiClient apiClient = ApiClient();

  // Check if the user is logged in locally
  static Future<bool> isLoggedIn() async {
    String? token = await TokenManager.getToken();
    return token != null && token.isNotEmpty;
  }

  // Set login status
  static Future<void> setLoggedIn(bool value, {String? token}) async {
    if (value && token != null) {
      await TokenManager.saveToken(token);
    } else {
      await TokenManager.clearToken();
    }
  }

  // Perform login via API
  Future<Map<String, dynamic>> login(String username, String password,details) async {
    final location = details['country']+ ',' + details['state'];
    try {
      final response = await apiClient.dio.post(
        '/login/mobile',
        data: {
          'username': username,
          'password': password,
          "device_name": details['device_name'],
          "device_type": details['device_type'],    
          "ip_address": details['ip_address'],
          "user_agent": details['user_agent'],
          "latitude" : details['latitude'],
          "longitude" : details['longitude'],
          "location" : location,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];

        await setLoggedIn(true, token: data['token']);
        return data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          error: 'Failed to login: ${response.statusMessage}',
        );
      }
    } on DioException catch (e) {
     
      rethrow; // Preserve the original exception
    }
  }

  // Send OTP
  Future<bool> sendOtp(String username) async {
    try {
      final response = await apiClient.dio.post(
        '/reset/send/otp',
        data: {
          'username': username,
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('Failed to send OTP: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception(e.response?.data['message'] ?? 'Unknown error');
    }
  }

  // Verify OTP
  Future<Map<String, dynamic>> verifyOtp(String username, String otp) async {
    try {
      final response = await apiClient.dio.post(
        '/verfy/otp',
        data: {
          'username': username,
          'code': otp,
        },
      );
     
      if (response.statusCode == 200) {
        final token = response.data['data']['token'];
        await setLoggedIn(true, token: token);
        return {'success': true, 'token': token};
      } else {
       
        throw Exception('Failed to verify OTP: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception(e.response?.data['message'] ?? 'Unknown error');
    }
  }

Future<Map<String, dynamic>> verifyTwoFactorOtp(String otp,details) async {
   final location = details['country']+ ',' + details['state'];
    String? token = await TokenManager.getToken();
      if (token == null) throw Exception('User is not authenticated.');
    try {
      final response = await apiClient.dio.post(
        '/user/verify/twofactor',
        data: {
          "device_name": details['device_name'],
          "device_type": details['device_type'],    
          "ip_address": details['ip_address'],
          "user_agent": details['user_agent'],
          "latitude" : details['latitude'],
          "longitude" : details['longitude'],
          "location" : location,
          'code': otp,
        },
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );
      if (response.statusCode == 200) {
        return {'success': true};
      } else {
       
        throw Exception('Failed to verify OTP: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception(e.response?.data['message'] ?? 'Unknown error');
    }
  }

  // Reset Password
  Future<bool> resetPassword(String username, String newPassword) async {
    try {
      String? token = await TokenManager.getToken();
      if (token == null) throw Exception('User is not authenticated.');

      final response = await apiClient.dio.post(
        '/user/reset/password',
        data: {
          'username': username,
          'newPassword': newPassword,
        },
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('Failed: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Error: ${e.response?.data ?? e.message}');
    }
  }

  // Get Profile
  Future<Map<String, dynamic>> getProfile() async {
    try {
      String? token = await TokenManager.getToken();
      if (token == null) throw Exception('User is not authenticated.');

      final response = await apiClient.dio.get(
        '/profile',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to fetch profile: ${response.statusMessage}');
      }
    } on DioException catch (e) {
     
      throw Exception('Error: ${e.response?.data ?? e.message}');
    }
  }

  // Logout user
  static Future<void> logout() async {
    await setLoggedIn(false);
  }
}
