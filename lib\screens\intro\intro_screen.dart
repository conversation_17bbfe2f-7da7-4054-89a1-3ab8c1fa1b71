// import 'package:flutter/material.dart';

// class IntroScreen extends StatefulWidget {
//   const IntroScreen({Key? key}) : super(key: key);

//   @override
//   State<IntroScreen> createState() => _IntroScreenState();
// }

// class _IntroScreenState extends State<IntroScreen>
//     with TickerProviderStateMixin {
//   final PageController _pageController = PageController();
//   int _currentPage = 0;
//   late AnimationController _fadeController;
//   late Animation<double> _fadeAnimation;

//   final List<Map<String, String>> _introSteps = [
//     {
//       'title': 'Welcome to Our App',
//       'description':
//           'Discover a seamless way to manage guest visits and inventions effortlessly. Stay organized and enhance your experience with our intuitive platform.',
//       'image': 'assets/images/rb_1.png'
//     },
//     {
//       'title': 'Easy & Efficient Tracking',
//       'description':
//           'Keep track of every guest entry and invention detail in one place. Our user-friendly interface ensures smooth and quick data management.',
//       'image': 'assets/images/rb_2.png'
//     },
//     {
//       'title': 'Smart Invention Logging',
//       'description':
//           'Effortlessly log and manage guest inventions with precision. Our system ensures every detail is captured accurately for easy reference and tracking.',
//       'image': 'assets/images/rb_3.png'
//     },
//   ];

//   @override
//   void initState() {
//     super.initState();
//     _fadeController = AnimationController(
//       duration: const Duration(milliseconds: 300),
//       vsync: this,
//     );
//     _fadeAnimation =
//         Tween<double>(begin: 0.0, end: 1.0).animate(_fadeController);
//     _fadeController.forward();
//   }

//   @override
//   void dispose() {
//     _pageController.dispose();
//     _fadeController.dispose();
//     super.dispose();
//   }

//   void _onNextPressed() {
//     if (_currentPage < _introSteps.length - 1) {
//       _fadeController.reverse().then((_) {
//         _pageController.nextPage(
//           duration: const Duration(milliseconds: 300),
//           curve: Curves.easeInOut,
//         );
//         _fadeController.forward();
//       });
//     } else {
//       _onFinish();
//     }
//   }

//   void _onFinish() async {
//     await Future.delayed(Duration.zero); // Replace with your auth service
//     if (!mounted) return;
//     Navigator.pushReplacementNamed(context, '/login');
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Container(
//         decoration: BoxDecoration(
//           gradient: LinearGradient(
//             begin: Alignment.topLeft,
//             end: Alignment.bottomRight,
//             colors: [
//               const Color(0xFF4158D0),
//               const Color(0xFFC850C0),
//               Colors.orange.shade400,
//             ],
//           ),
//         ),
//         child: SafeArea(
//           child: Column(
//             children: [
//               Expanded(
//                 child: PageView.builder(
//                   controller: _pageController,
//                   onPageChanged: (index) {
//                     setState(() {
//                       _currentPage = index;
//                       _fadeController.reset();
//                       _fadeController.forward();
//                     });
//                   },
//                   itemCount: _introSteps.length,
//                   itemBuilder: (context, index) {
//                     final step = _introSteps[index];
//                     return FadeTransition(
//                       opacity: _fadeAnimation,
//                       child: IntroStep(
//                         title: step['title']!,
//                         description: step['description']!,
//                         imagePath: step['image']!,
//                       ),
//                     );
//                   },
//                 ),
//               ),
//               _buildPageIndicator(),
//               _buildNextButton(),
//               const SizedBox(height: 40),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildPageIndicator() {
//     return Container(
//       padding: const EdgeInsets.symmetric(vertical: 20),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: List.generate(
//           _introSteps.length,
//           (index) => AnimatedContainer(
//             duration: const Duration(milliseconds: 300),
//             margin: const EdgeInsets.symmetric(horizontal: 4),
//             height: 8,
//             width: _currentPage == index ? 24 : 8,
//             decoration: BoxDecoration(
//               color:
//                   Colors.white.withOpacity(_currentPage == index ? 0.9 : 0.4),
//               borderRadius: BorderRadius.circular(4),
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildNextButton() {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 24),
//       child: Container(
//         width: double.infinity,
//         height: 55,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(16),
//           gradient: LinearGradient(
//             colors: [
//               Colors.white.withOpacity(0.2),
//               Colors.white.withOpacity(0.3),
//             ],
//           ),
//           boxShadow: [
//             BoxShadow(
//               color: Colors.black.withOpacity(0.1),
//               blurRadius: 10,
//               offset: const Offset(0, 5),
//             ),
//           ],
//         ),
//         child: ElevatedButton(
//           onPressed: _onNextPressed,
//           style: ElevatedButton.styleFrom(
//             backgroundColor: Colors.transparent,
//             shadowColor: Colors.transparent,
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(16),
//             ),
//           ),
//           child: Text(
//             _currentPage == _introSteps.length - 1 ? 'Get Started' : 'Next',
//             style: const TextStyle(
//               color: Colors.white,
//               fontSize: 18,
//               fontWeight: FontWeight.bold,
//               letterSpacing: 1,
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

// class IntroStep extends StatelessWidget {
//   final String title;
//   final String description;
//   final String imagePath;

//   const IntroStep({
//     Key? key,
//     required this.title,
//     required this.description,
//     required this.imagePath,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Container(
//           margin: const EdgeInsets.symmetric(horizontal: 24),
//           padding: const EdgeInsets.all(20),
//           decoration: BoxDecoration(
//             color: Colors.white.withOpacity(0.1),
//             borderRadius: BorderRadius.circular(20),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withOpacity(0.1),
//                 blurRadius: 20,
//                 spreadRadius: 5,
//               ),
//             ],
//           ),
//           child: Image.asset(
//             imagePath,
//             height: 280,
//             width: 280,
//             fit: BoxFit.contain,
//           ),
//         ),
//         const SizedBox(height: 40),
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 24),
//           child: Text(
//             title,
//             style: const TextStyle(
//               fontSize: 32,
//               fontWeight: FontWeight.bold,
//               color: Colors.white,
//               letterSpacing: 0.5,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//         const SizedBox(height: 16),
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 40),
//           child: Text(
//             description,
//             style: TextStyle(
//               fontSize: 16,
//               color: Colors.white.withOpacity(0.8),
//               height: 1.5,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       ],
//     );
//   }
// }

import 'package:flutter/material.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({Key? key}) : super(key: key);

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  final List<Map<String, String>> _introSteps = [
    {
      'title': 'Welcome to Our App',
      'description':
          'Discover a seamless way to manage guest visits and inventions effortlessly. Stay organized and enhance your experience with our intuitive platform.',
      'image': 'assets/images/rb_1.png'
    },
    {
      'title': 'Easy & Efficient Tracking',
      'description':
          'Keep track of every guest entry and invention detail in one place. Our user-friendly interface ensures smooth and quick data management.',
      'image': 'assets/images/rb_2.png'
    },
    {
      'title': 'Smart Invention Logging',
      'description':
          'Effortlessly log and manage guest inventions with precision. Our system ensures every detail is captured accurately for easy reference and tracking.',
      'image': 'assets/images/rb_3.png'
    },
  ];

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_fadeController);
    _fadeController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _onNextPressed() {
    if (_currentPage < _introSteps.length - 1) {
      _fadeController.reverse().then((_) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        _fadeController.forward();
      });
    } else {
      _onFinish();
    }
  }

  void _onFinish() async {
    await Future.delayed(Duration.zero);
    if (!mounted) return;
    Navigator.pushReplacementNamed(context, '/login');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF2B2D3E),
              Color(0xFF1F2634),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                      _fadeController.reset();
                      _fadeController.forward();
                    });
                  },
                  itemCount: _introSteps.length,
                  itemBuilder: (context, index) {
                    final step = _introSteps[index];
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: IntroStep(
                        title: step['title']!,
                        description: step['description']!,
                        imagePath: step['image']!,
                      ),
                    );
                  },
                ),
              ),
              _buildPageIndicator(),
              _buildNextButton(),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPageIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          _introSteps.length,
          (index) => AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: const EdgeInsets.symmetric(horizontal: 4),
            height: 8,
            width: _currentPage == index ? 24 : 8,
            decoration: BoxDecoration(
              color:
                  _currentPage == index ? Color(0xFF3F4251) : Color(0xFF2B2D3E),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNextButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Container(
        width: double.infinity,
        height: 55,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF3F4251),
              Color(0xFF2B2D3E),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: _onNextPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: Text(
            _currentPage == _introSteps.length - 1 ? 'Get Started' : 'Next',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              letterSpacing: 1,
            ),
          ),
        ),
      ),
    );
  }
}

class IntroStep extends StatelessWidget {
  final String title;
  final String description;
  final String imagePath;

  const IntroStep({
    Key? key,
    required this.title,
    required this.description,
    required this.imagePath,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 24),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Color(0xFF3F4251).withOpacity(0.3),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 20,
                spreadRadius: 5,
              ),
            ],
          ),
          child: Image.asset(
            imagePath,
            height: 280,
            width: 280,
            fit: BoxFit.contain,
          ),
        ),
        const SizedBox(height: 40),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 0.5,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40),
          child: Text(
            description,
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withOpacity(0.8),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
