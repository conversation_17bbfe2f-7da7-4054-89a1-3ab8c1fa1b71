import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:guest_mobile_app/controller/alert_calender_bloc/alert_calender_controller_bloc.dart';
import 'package:guest_mobile_app/core/services/auth_service.dart';
import 'package:guest_mobile_app/core/services/requests_service.dart';
import 'package:guest_mobile_app/core/utils/tokenManager.dart';
import 'package:guest_mobile_app/screens/requests/request_details.dart';
import 'package:guest_mobile_app/widgets/custom_drawer.dart';
import 'package:guest_mobile_app/widgets/guest_from_drawer.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'dart:async';

class RequestsScreen extends StatefulWidget {
  const RequestsScreen({Key? key}) : super(key: key);

  @override
  State<RequestsScreen> createState() => _RequestsScreenState();
}

class _RequestsScreenState extends State<RequestsScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  List<Map<String, dynamic>> guests = [];
  bool _isLoading = true;
  String selectedCategory = 'Individual';
  Timer? _debounceTimer;
  Map<String, dynamic>? _profileData;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _fetchGuests();
    _fetchProfile();
    // Add listener to search controller
    _searchController.addListener(_onSearchChanged);
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.35),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _animationController.forward();
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) {
      _debounceTimer!.cancel();
    }
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _fetchGuests(isSearch: true); // Add isSearch parameter here
    });
  }

  String _formatTime(String timeString) {
    try {
      DateTime parsedTime = DateTime.parse(timeString);
      return DateFormat.jm().format(parsedTime);
    } catch (e) {
     
      return timeString;
    }
  }

  Future<void> _fetchGuests({bool isSearch = false}) async {
    if (!isSearch) {
      setState(() => _isLoading = true);
    }

    try {
      String? token = await TokenManager.getToken();
      final requestsService = RequestsService();
      final response =
          await requestsService.getRequests(token, _searchController.text);

      if (response['data'] != null && response['data'] is List) {
        _processGuestsData(response['data']);
      } else {
        throw Exception('Invalid data format');
      }
    } catch (e) {
     
    } finally {
      if (!isSearch) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _processGuestsData(List data) {
    final statusLabels = [
      '',
      AppLocalizations.of(context)!.draft,
      AppLocalizations.of(context)!.onprocess,
      AppLocalizations.of(context)!.approved_tx,
      AppLocalizations.of(context)!.rejected_tx,
      AppLocalizations.of(context)!.entered_tx,
      AppLocalizations.of(context)!.canceled,
    ];

    setState(() {
      guests = sortByStatus(data.map((guest) {
        String rawTime = guest['invitedTime'] ?? '14:00';
       
        return {
          'id': guest['requestId'] ?? '0',
          'name': guest['guestName'] ?? 'Unknown',
          'phone': guest['guestPhone'] ?? '****** 567 890',
          'guestId': guest['guestId'] ?? '0',
          'invitedPlace': guest['area'] ?? 'Conference Hall',
          'placeName': guest['place'] ?? 'Grand Hotel',
          'status': statusLabels[guest['guestStatus'] ?? 1],
          'status_value': guest['guestStatus'] ?? 0,
          'invitedDate': guest['invitedDate'] ?? '2024-06-30',
          'invitedTime': _formatTime(rawTime),
          'avatarColor': Colors.blue,
          'category': guest['guest_type'] == 1 ? 'Individual' : 'Group',
          'senderName': guest['senderName'] ?? '',
          'senderId': guest['senderId'] ?? '',
          'haveCar': guest['haveCar'] ?? 0,
          'haveItem': guest['haveItem'] ?? 0,
          'haveLaptop': guest['haveLaptop'] ?? 0,
          'serial_number': guest['serial_number'] ?? '',
          'others': guest['others'] ?? '',
          'car_plate': guest['car_plate'] ?? '',
          'isMoreThanOneDay': guest['isMoreThanOneDay'] ?? 0,
          'numberOfDays': guest['numberOfDays'] ?? 0,
        };
      }).toList());
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> sortByStatus(List<Map<String, dynamic>> arr) {
    arr.sort((a, b) {
      int statusA = a['status_value'] ?? 0; // Extract status_value
      int statusB = b['status_value'] ?? 0; // Extract status_value
      return statusA.compareTo(statusB); // Use compareTo for integer comparison
    });
    return arr;
  }

  Future<void> _fetchProfile() async {
    try {
      final authService = AuthService();
      final profileData = await authService.getProfile();

      setState(() {
        _profileData = profileData['data'];
        _isLoading = false;
      });
    } catch (e) {
     
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey, // Set the GlobalKey here
      backgroundColor: const Color(0xFFF8F9FE),
      appBar: _buildAppBar(),
      drawer: _isLoading
          ? const Drawer(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          : CustomDrawer(profileData: _profileData ?? {}),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Padding(
              padding: const EdgeInsets.only(top: 16.0), // Add top margin
              child: Column(
                children: [
                  _buildSearchBar(),
                  Expanded(
                    child: _isLoading
                        ? _buildLoadingIndicator()
                        : _buildGuestList(),
                  ),
                ],
              ),
            ),
      floatingActionButton:
          _isLoading ? _buildFloatingActionButton() : Container(),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      leading: IconButton(
        icon: const Icon(Icons.menu, color: Color(0xFF4A4A4A)),
        onPressed: () {
          _scaffoldKey.currentState?.openDrawer();
        },
      ),
      title: Text(
        AppLocalizations.of(context)!.guestRequests,
        style: const TextStyle(
          fontSize: 20,
          color: Color(0xFF4A4A4A),
          fontWeight: FontWeight.w500,
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.more_horiz, color: Color(0xFF4A4A4A)),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
        color: Colors.white,
      ),
      child: Row(
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child: Icon(Icons.search, color: Color(0xFF9E9E9E), size: 20),
          ),
          Expanded(
            child: TextField(
              controller: _searchController,
              style: const TextStyle(
                fontSize: 15,
                color: Color(0xFF4A4A4A),
              ),
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)!.searchGuests,
                hintStyle: const TextStyle(
                  color: Color(0xFF9E9E9E),
                  fontSize: 15,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          if (_searchController.text.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.close, color: Color(0xFF9E9E9E), size: 20),
              onPressed: () {
                _searchController.clear();
                _fetchGuests();
              },
            ),
        ],
      ),
    );
  }

  Widget _buildGuestList() {
    if (guests.isEmpty) {
      return Center(
        child: Text(AppLocalizations.of(context)!.noGuestsFound),
      );
    }

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: guests.length,
          itemBuilder: (context, index) {
            return _buildGuestCard(guests[index], index);
          },
        ),
      ),
    );
  }

  Widget _buildGuestCard(Map<String, dynamic> guest, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.1)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => RequestDetailsScreen(
                      guest: guest,
                      onRequestSent: () {
                        _fetchGuests();
                      }),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Stack(
                        children: [
                          CircleAvatar(
                            radius: 24,
                            backgroundColor:
                                guest['avatarColor'].withOpacity(0.8),
                            child: Text(
                              guest['name'][0],
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          if (guest['category'] == 'Group')
                            Positioned(
                              right: 0,
                              bottom: 0,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.group,
                                  size: 14,
                                  color: Color(0xFF2196F3),
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              guest['name'],
                              style: const TextStyle(
                                fontSize: 16,
                                color: Color(0xFF4A4A4A),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              guest['phone'],
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      ),
                      _buildStatusChip(guest['status']),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildDetailsSection(guest),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    final isActive = status == AppLocalizations.of(context)!.approved_tx;
    final isEntered = status == AppLocalizations.of(context)!.entered_tx;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isActive
            ? const Color(0xFFE8F5E9)
            : isEntered
                ? Color.fromARGB(198, 187, 223, 220)
                : const Color(0xFFFFF3E0),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        status,
        style: TextStyle(
          fontSize: 13,
          color: isActive
              ? const Color(0xFF2E7D32)
              : isEntered
                  ? Colors.teal
                  : const Color(0xFFF57C00),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildDetailsSection(Map<String, dynamic> guest) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FE),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildDetailRow(
            AppLocalizations.of(context)!.location,
            '${guest['invitedPlace']} - ${guest['placeName']}',
            Icons.location_on_outlined,
          ),
          const SizedBox(height: 8),
          _buildDetailRow(
            AppLocalizations.of(context)!.dateTime,
            '${guest['invitedDate']} at ${guest['invitedTime']}',
            Icons.access_time,
          ),
          const SizedBox(height: 8),
          _buildDetailRow(
            AppLocalizations.of(context)!.sentBy,
            '${guest['senderName']}',
            Icons.supervised_user_circle,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: const Color(0xFF9E9E9E)),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  color: Color(0xFF9E9E9E),
                  fontSize: 12,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Color(0xFF4A4A4A),
                  fontSize: 13,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () {
        showFullScreenBottomDrawer(context, () {
          _fetchGuests();
        }, selectedCategory);
      },
      backgroundColor: const Color(0xFF2196F3),
      label: Row(
        children: [
          const Icon(Icons.add, color: Colors.white),
          const SizedBox(width: 8),
          Text(
            selectedCategory == 'Individual'
                ? AppLocalizations.of(context)!.addIndividual
                : AppLocalizations.of(context)!.addGroup,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

void showFullScreenBottomDrawer(
    BuildContext context, VoidCallback onGuestAdded, String category) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return FractionallySizedBox(
        heightFactor: 0.92,
        child: BlocProvider(
          create: (context) => AlertCalenderControllerBloc(),
          child:
              GuestFormDrawer(onGuestAdded: onGuestAdded, category: category),
        ),
      );
    },
  );
}
