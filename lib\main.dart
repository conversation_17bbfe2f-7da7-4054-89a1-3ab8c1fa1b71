import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:guest_mobile_app/core/services/auth_service.dart';
import 'package:guest_mobile_app/providers/appLifecycleService.dart';
import 'routes/app_router.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';
import 'package:guest_mobile_app/providers/language_provider.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  bool isLoggedIn = await AuthService.isLoggedIn();
  AppLifecycleService().init();
  runApp(
    ChangeNotifierProvider(
      create: (context) => LanguageProvider(),
      child: MyApp(initialRoute: isLoggedIn ? '/login' : '/intro'),
    ),
  );
}

class MyApp extends StatefulWidget {
  final String initialRoute;

  const MyApp({Key? key, required this.initialRoute}) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    checkEmulatorAndNotify(); // Check if the app is running on an emulator
  }

  void checkEmulatorAndNotify() async {
    if (kIsWeb) {
      // On web, skip emulator check
      return;
    }

    final deviceInfo = DeviceInfoPlugin();
    bool isEmulator = false;

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      isEmulator = androidInfo.isPhysicalDevice == false;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      isEmulator = iosInfo.isPhysicalDevice == false;
    }

    if (isEmulator) {
      // Notify user that they are on an emulator
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Emulator Detected'),
            content: const Text(
                'This app is running on an emulator. It will now close.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  SystemChannels.platform.invokeMethod('SystemNavigator.pop');
                },
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      locale: Provider.of<LanguageProvider>(context).locale,
      supportedLocales: const [
        Locale('en', ''),
        Locale('am', ''),
      ],
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      localeResolutionCallback: (locale, supportedLocales) {
        if (locale == null) return supportedLocales.first;
        for (var supportedLocale in supportedLocales) {
          if (supportedLocale.languageCode == locale.languageCode) {
            return supportedLocale;
          }
        }
        return supportedLocales.first; // Default to English
      },
      title: 'Guest Mobile App',
      navigatorKey: navigatorKey,
      initialRoute: widget.initialRoute,
      routes: AppRouter.routes,
      onUnknownRoute: (settings) {
        return MaterialPageRoute(
          builder: (context) => Scaffold(
            appBar: AppBar(
              title: Text('Unknown Route'),
            ),
            body: Center(
              child: Text('Page not found'),
            ),
          ),
        );
      },
    );
  }
}
