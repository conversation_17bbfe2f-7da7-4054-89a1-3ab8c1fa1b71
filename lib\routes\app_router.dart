import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:guest_mobile_app/controller/alert_calender_bloc/alert_calender_controller_bloc.dart';
import 'package:guest_mobile_app/screens/auth/forgot_password_screen.dart';
import 'package:guest_mobile_app/screens/auth/twofactor_otp_screen.dart';

import 'package:guest_mobile_app/screens/guests/invites.dart';
import 'package:guest_mobile_app/screens/intro/intro_screen.dart';
import 'package:guest_mobile_app/screens/requests/requests.dart';
import 'package:guest_mobile_app/screens/settings/account.dart';
import 'package:guest_mobile_app/screens/vipguestRequests/vip_requests.dart';
import '../screens/home/<USER>';
import '../screens/auth/login_screen.dart';

class AppRouter {
  static Map<String, WidgetBuilder> routes = {
    '/home': (context) => HomeScreen(),
    '/login': (context) => const  LoginScreen(),
    '/intro': (context) => const IntroScreen(),
    '/requests': (context) => const RequestsScreen(),
    '/vipRequests': (context) => const VipRequestsScreen(),
    '/invites': (context) => BlocProvider(
        create: (context) => AlertCalenderControllerBloc(),
        child: const InviteGuestsScreen()),
    '/forgot-password': (context) => ForgotPasswordScreen(),
    '/change-password': (context) => const ChangePasswordScreen(),
    '/otp': (context) =>  TwofactorOtpScreen(),
  };
}
