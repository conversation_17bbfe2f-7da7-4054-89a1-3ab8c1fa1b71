import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:guest_mobile_app/providers/language_provider.dart';

class AppBarWithLanguageSwitch extends StatelessWidget implements PreferredSizeWidget {
  const AppBarWithLanguageSwitch({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      actions: [
        PopupMenuButton<Locale>(
          icon: const Icon(Icons.language, color: Color.fromARGB(255, 33, 32, 32)),
          onSelected: (Locale locale) {
            languageProvider.setLocale(locale);
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: Locale('en'),
              child: Text('English'),
            ),
            const PopupMenuItem(
              value: Locale('am'),
              child: Text('Amharic'),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
