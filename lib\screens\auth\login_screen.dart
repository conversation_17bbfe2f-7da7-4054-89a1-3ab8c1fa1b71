import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:guest_mobile_app/controller/alert_calender_bloc/alert_calender_controller_bloc.dart';
import 'package:guest_mobile_app/core/services/auth_service.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';
import 'package:guest_mobile_app/providers/language_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart' show kIsWeb;

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  final Locale _locale = const Locale('en');

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.35),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _controller.forward();
    BlocProvider(
      create: (context) => AlertCalenderControllerBloc(),
      child: const LoginScreen(),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<Map<String, dynamic>> getDeviceDetails() async {
  DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
  String deviceName = '';
  String deviceType = '';

  if (kIsWeb) {
    WebBrowserInfo webInfo = await deviceInfo.webBrowserInfo;
    deviceName = webInfo.userAgent ?? 'Web';
    deviceType = 'Web';
  } else {
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceName = androidInfo.model;
      deviceType = 'Android';
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceName = iosInfo.name;
      deviceType = 'iOS';
    }
  }

  // Get IP Address
  String ip = 'Unknown';
  try {
    final response =
        await http.get(Uri.parse('https://api64.ipify.org?format=json'));
    ip = jsonDecode(response.body)['ip'] ?? 'Unknown';
  } catch (_) {
    // IP fetch error
  }

  // Get Current Location
  Position? position;
  try {
    position = await Geolocator.getCurrentPosition();
  } catch (_) {
    // Location permission error or denied
  }

  // Reverse Geocode
  String country = 'Unknown';
  String state = 'Unknown';
  if (position != null) {
    try {
      final geoResponse = await http.get(Uri.parse(
          'https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.latitude}&lon=${position.longitude}&addressdetails=1'));
      final geoData = jsonDecode(geoResponse.body);
      country = geoData['address']?['country'] ?? 'Unknown';
      state = geoData['address']?['state'] ?? 'Unknown';
    } catch (_) {
      // Geocode error
    }
  }

  return {
    "device_type": deviceType,
    "device_name": deviceName,
    "ip_address": ip,
    "latitude": position?.latitude,
    "longitude": position?.longitude,
    "country": country,
    "state": state,
  };
}

  void _login() async {
    PermissionStatus status = await Permission.location.request();

    if (status.isGranted) {
      if (!_formKey.currentState!.validate()) return;

      setState(() => _isLoading = true);

      try {
        final authService = AuthService();
        final details = await getDeviceDetails();
        final response = await authService.login(
          _usernameController.text,
          _passwordController.text,
          details
        );
        if (response['twofactor'] == true) {
          Navigator.pushNamed(context, '/otp');
        } else {
          Navigator.pushReplacementNamed(context, '/home');
        }
      } catch (e) {
        String errorMessage = 'Login failed. Please try again.';
        if (e is DioException) {
          
          errorMessage = e.response?.data['message'] ?? errorMessage;
        } else {
         
        }

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red.shade400,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          margin: const EdgeInsets.all(16),
        ));
      } finally {
        if (mounted) setState(() => _isLoading = false);
      }
    } else if (status.isDenied) {
      // Permission denied
     
    } else if (status.isPermanentlyDenied) {
      // Permission is permanently denied, guide the user to settings
     
      openAppSettings();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF2B2D3E),
                Color(0xFF1F2634),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 15,
                spreadRadius: 1,
                offset: const Offset(0, 3),
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                spreadRadius: 0,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            automaticallyImplyLeading: false,
            toolbarHeight: kToolbarHeight,
            // Add logo here
            leading: Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Container(
                width: 50, // Adjust the size as needed
                height: 50,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white, // White background for the circle
                ),
                child: Padding(
                  padding: const EdgeInsets.all(
                      8.0), // Adjust padding to fit the logo well
                  child: Image.asset(
                    'assets/images/etv.png', // Replace with your logo path
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),

            actions: [
              PopupMenuButton<Locale>(
                icon: const Icon(Icons.translate_rounded, color: Colors.white),
                onSelected: (locale) => _changeLanguage(locale),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: Locale('en'),
                    child: Text('English 🇺🇸'),
                  ),
                  const PopupMenuItem(
                    value: Locale('am'),
                    child: Text('Amharic 🇪🇹'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF2B2D3E),
              Color(0xFF1F2634),
            ],
          ),
        ),
        child: SafeArea(
          child: SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: CustomScrollView(
                slivers: [
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            _buildHeader(),
                            const SizedBox(height: 40),
                            _buildLoginForm(),
                            const SizedBox(height: 20),
                            _buildLoginButton(),
                            _buildForgotPassword(),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _changeLanguage(Locale locale) {
    Provider.of<LanguageProvider>(context, listen: false).setLocale(locale);
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white24,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                spreadRadius: 5,
              ),
            ],
          ),
          child: const Icon(
            Icons.lock_outline_rounded,
            size: 60,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          AppLocalizations.of(context)!.welcome,
          style: const TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            letterSpacing: 0.5,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context)!.signIn,
          style: TextStyle(
            fontSize: 16,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Column(
      children: [
        _buildTextField(
          controller: _usernameController,
          label: AppLocalizations.of(context)!.username,
          icon: Icons.person_outline_rounded,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return AppLocalizations.of(context)!.validationUsername;
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _passwordController,
          label: AppLocalizations.of(context)!.password,
          icon: Icons.lock_outline_rounded,
          isPassword: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return AppLocalizations.of(context)!.validationPassword;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    bool isPassword = false,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: isPassword && !_isPasswordVisible,
      style: const TextStyle(color: Colors.white, fontSize: 16),
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(color: Colors.white.withOpacity(0.8)),
        prefixIcon: Icon(icon, color: Colors.white70),
        suffixIcon: isPassword
            ? IconButton(
                icon: Icon(
                  _isPasswordVisible
                      ? Icons.visibility_rounded
                      : Icons.visibility_off_rounded,
                  color: Colors.white70,
                ),
                onPressed: () {
                  setState(() => _isPasswordVisible = !_isPasswordVisible);
                },
              )
            : null,
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: Color(0xFF3F4251)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: Color(0xFF4A4D63), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.red.shade300),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.red.shade400, width: 2),
        ),
        filled: true,
        fillColor: Color(0xFF2B2D3E).withOpacity(0.3),
      ),
    );
  }

  Widget _buildLoginButton() {
    return Container(
      width: double.infinity,
      height: 55,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF3F4251),
            Color(0xFF2B2D3E),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _login,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 3,
                ),
              )
            : Text(
                AppLocalizations.of(context)!.signInbtn,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Widget _buildForgotPassword() {
    return TextButton(
      onPressed: _isLoading
          ? null
          : () {
              Navigator.pushNamed(context, '/forgot-password');
            },
      style: TextButton.styleFrom(
        foregroundColor: Colors.white.withOpacity(0.8),
      ),
      child: Text(
        AppLocalizations.of(context)!.forgotPassword,
        style: TextStyle(fontSize: 16),
      ),
    );
  }
}
