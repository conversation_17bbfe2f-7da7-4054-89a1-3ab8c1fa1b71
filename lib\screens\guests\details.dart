//import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:guest_mobile_app/controller/alert_calender_bloc/alert_calender_controller_bloc.dart';
import 'package:guest_mobile_app/core/constants/constants.dart';
import 'package:guest_mobile_app/core/services/guests_service.dart';
import 'package:guest_mobile_app/core/utils/tokenManager.dart';
import 'package:guest_mobile_app/widgets/custom_dialog.dart';
import 'package:guest_mobile_app/widgets/guest_from_drawer_edit.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';
import 'package:path_provider/path_provider.dart'
    show getApplicationDocumentsDirectory;

class GuestDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> guest;
  final VoidCallback onRequestSent;
  GuestDetailsScreen(
      {Key? key, required this.guest, required this.onRequestSent})
      : super(key: key);
  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  _GuestDetailsScreenState createState() => _GuestDetailsScreenState();
}

class _GuestDetailsScreenState extends State<GuestDetailsScreen>
    with TickerProviderStateMixin {
  final double infoHeight = 240.0;
  late AnimationController animationController;
  late Animation<double> animation;
  double opacity1 = 0.0;
  double opacity2 = 0.0;
  double opacity3 = 0.0;
  String? selectedName;
  bool _isLoading = true;
  String? selectedPhone;
  int? selectedId;
  List<Map<String, dynamic>> peopleList = [];
  String fileUrl = '';
  late Map<String, dynamic> guestData;
  late String existingImageUrl;
  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
        duration: const Duration(milliseconds: 1000), vsync: this);
    animation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
        parent: animationController,
        curve: Interval(0, 1.0, curve: Curves.fastOutSlowIn)));
    setData();
    guestData = Map<String, dynamic>.from(widget.guest);
   
    _fetchGuests();
    if (guestData['category'] != 'Individual') [_fetchAttacments()];
  }

  Future<void> _fetchGuests() async {
    setState(() => _isLoading = true);

    try {
      String? token = await TokenManager.getToken();
      final guestService = GuestService();
      final response = await guestService.getApprover(token);

      if (response['data'] != null && response['data'] is List) {
        _processGuestsData(response['data']);
      } else {
        throw Exception('Invalid data format');
      }
    } catch (e) {
    
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchAttacments() async {
    setState(() => _isLoading = true);

    try {
      String? token = await TokenManager.getToken();
      final guestService = GuestService();
      final response =
          await guestService.getAttachments(token, guestData['id']);

      if (response['data'] != null) {
        setState(() => fileUrl =
            AppConstants.apiBaseIP + "/" + response['data']['filePath']);
      } else {
        throw Exception('Invalid data format');
      }
    } catch (e) {
      
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _processGuestsData(List data) {
    const statusLabels = [
      '',
      'Draft',
      'በመጠበቅ ላይ',
      'Approved',
      'Rejected',
      'Canceled'
    ];

    setState(() {
      peopleList = data.map((user) {
        return {
          'id': user['id'] ?? '0',
          'name': user['fullName'] ?? 'Unknown',
          'phone': user['phoneNumber'] ?? '****** 567 890',
        };
      }).toList();
    });
  }

  void _approveSubmit() async {
    setState(() => _isLoading = true);
    try {
      String? token = await TokenManager.getToken();

      int guest_type = guestData['category'] == 'Individual' ? 1 : 2;
      int? id = guestData['id'];
      final guestService = GuestService();
      final response =
          await guestService.saveApproveChanges(token, id, guest_type);

      if (response['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully made changes!'),
            backgroundColor: Colors.green,
          ),
        );
        // widget.onRequestSent();
        widget.onRequestSent();
        Navigator.pop(context);
      } else {
        throw Exception(response['message'] ?? 'Failed to save guest');
      }
    } catch (e) {
     
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(e.toString()),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _handleSubmit() async {
    setState(() => _isLoading = true);
    try {
      String? token = await TokenManager.getToken();
      int? approverId = selectedId;
      int guest_type = guestData['category'] == 'Individual' ? 1 : 2;
      int guestId = guestData['id'];
      final guestService = GuestService();
      final response = await guestService.sendRequest(
          token, guest_type, guestId, approverId);

      if (response['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('send request successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onRequestSent();
        Navigator.pop(context);
      } else {
        throw Exception(response['message'] ?? 'Failed to save guest');
      }
    } catch (e) {
     
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(e.toString()),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void onCallback(Map<String, dynamic> guest) {
    _fetchAttacments();
    setState(() {
      guestData = guest;
      fileUrl = guest['fileUrl'];
    });
  }

  void _performCancel() async {
    setState(() => _isLoading = true);
    try {
      String? token = await TokenManager.getToken();
      int guest_type = guestData['category'] == 'Individual' ? 1 : 2;
      int? id = guestData['id'];
      final guestService = GuestService();
      final response =
          await guestService.saveCancelChanges(token, id, guest_type);

      if (response['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
         const SnackBar(
            content: Text('Successfully made changes!'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onRequestSent();
        Navigator.pop(context);
      } else {
        throw Exception(response['message'] ?? 'Failed to save guest');
      }
    } catch (e) {
     
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(e.toString()),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> setData() async {
    animationController.forward();
    await Future<dynamic>.delayed(const Duration(milliseconds: 200));
    setState(() {
      opacity1 = 1.0;
    });
    await Future<dynamic>.delayed(const Duration(milliseconds: 200));
    setState(() {
      opacity2 = 1.0;
    });
    await Future<dynamic>.delayed(const Duration(milliseconds: 200));
    setState(() {
      opacity3 = 1.0;
    });
  }

  Color getStatusColor(int status) {
    switch (status) {
      case 1:
        return Colors.blue;
      case 3:
        return Colors.green;
      case 2:
        return Colors.orange;
      case 4:
        return Colors.red;
      case 5:
        return Colors.teal;
      case 6:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildStatusBar() {
    final statusColor = getStatusColor(guestData['status_value']);
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: statusColor.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            guestData['status'],
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildModernActionButtons({
    required VoidCallback onCancel,
    required VoidCallback onApprove,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        if (!guestData['isHead']) ...[
          guestData['status_value'] == 2
              ? GestureDetector(
                  onTap: onCancel,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.red.shade400, Colors.red.shade600],
                      ),
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.shade200,
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.close, color: Colors.white),
                        const SizedBox(width: 8),
                         Text(
                          AppLocalizations.of(context)!.cancel,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : Container(),
        ],
        if (guestData['isHead']) ...[
          GestureDetector(
            onTap: onApprove,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color.fromARGB(255, 53, 163, 89),
                    Color.fromARGB(255, 31, 147, 54)
                  ],
                ),
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.shade200,
                    blurRadius: 6,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.close, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context)!.approved,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          )
        ]
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final double tempHeight = MediaQuery.of(context).size.height -
        (MediaQuery.of(context).size.width / 1.8) +
        24.0;

    return Container(
      color: Colors.white,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        floatingActionButton: !guestData['isHead']
            ? guestData['status_value'] == 1 ||
                    guestData['status_value'] == 4 ||
                    guestData['status_value'] == 6
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Container(
                        width: MediaQuery.of(context).size.width,
                        height: MediaQuery.of(context).size.height / 6,
                        child: _buildBottomBar(context)),
                  )
                : buildModernActionButtons(
                    onCancel: () => {showCancelConfirmation(context)},
                    onApprove: () => {showApproveConfirmation(context)})
            : guestData['status_value'] == 1
                ? buildModernActionButtons(
                    onCancel: () => {showCancelConfirmation(context)},
                    onApprove: () => {showApproveConfirmation(context)})
                : Container(),
        floatingActionButtonLocation:
            FloatingActionButtonLocation.miniCenterFloat,
        body: Stack(
          children: <Widget>[
            Column(
              children: <Widget>[
                // Add status bar at the top
                AspectRatio(
                  aspectRatio: 1.8,
                  child: Image.asset(
                    'assets/images/ej6k1vd8.png',
                    fit: BoxFit.cover,
                  ),
                ),
              ],
            ),
            Positioned(
              top: (MediaQuery.of(context).size.width / 1.8) - 24.0,
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(32.0),
                    topRight: Radius.circular(32.0),
                  ),
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      offset: const Offset(1.1, 1.1),
                      blurRadius: 10.0,
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.only(left: 8, right: 8),
                  child: SingleChildScrollView(
                    child: Container(
                      constraints: BoxConstraints(
                        minHeight: infoHeight,
                        maxHeight:
                            tempHeight > infoHeight ? tempHeight : infoHeight,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(25.0),
                        child: _buildContent(context),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            if (guestData['status_value'] != 5) ...[
              Positioned(
                top: (MediaQuery.of(context).size.width / 1.8) - 24.0 - 35,
                right: 35,
                child: ScaleTransition(
                  alignment: Alignment.center,
                  scale: CurvedAnimation(
                    parent: animationController,
                    curve: Curves.fastOutSlowIn,
                  ),
                  child: Card(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(50.0),
                    ),
                    elevation: 10.0,
                    child: Container(
                      width: 60,
                      height: 60,
                      child: Center(
                        child: IconButton(
                          icon: const Icon(Icons.edit, color: Colors.blue),
                          onPressed: () => {
                            showFullScreenBottomDrawer(context, () {
                              widget.onRequestSent();
                            }, guestData['category'], guestData, onCallback,
                                fileUrl)
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
            Positioned(
              top: MediaQuery.of(context).padding.top,
              left: 10,
              child: CircleAvatar(
                backgroundColor: const Color.fromARGB(255, 162, 191, 216),
                child: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void showCancelConfirmation(BuildContext context) {
    Navigator.of(context).push(CustomDialogRoute(
      builder: (context) => Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.gpp_bad_rounded,
                    size: 48,
                    color: Color.fromARGB(255, 175, 76, 76),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.confirmCancel,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    AppLocalizations.of(context)!.cancelMessage,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.close,
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          // _performApproval();
                          _performCancel();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              const Color.fromARGB(255, 175, 76, 76),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.cancel,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }

  void showApproveConfirmation(BuildContext context) {
    Navigator.of(context).push(CustomDialogRoute(
      builder: (context) => Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.check_circle_outline_sharp,
                    size: 48,
                    color: Color.fromARGB(255, 108, 255, 145),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.confirmApprove,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    AppLocalizations.of(context)!.approveMessage,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.close,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          // _performApproval();
                          _approveSubmit();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color.fromARGB(255, 76, 175, 91),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.approve,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }

  Widget _buildContent(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 100), // Add bottom padding
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildStatusBar(),
            const SizedBox(height: 24),
            _buildFeatures(),
            const SizedBox(height: 24),
            _buildDetails(),
            const SizedBox(height: 24),
            if (guestData['category'] == 'Individual' &&
                    guestData['haveCar'] == 1 ||
                guestData['category'] == 'Individual' &&
                    guestData['haveItem'] == 1 ||
                guestData['category'] == 'Individual' &&
                    guestData['haveLaptop'] == 1 ||
                guestData['isMoreThanOneDay'] == 1) ...[
              _buildAdditionalInfo(),
              const SizedBox(height: 24),
            ],
            if (fileUrl.isNotEmpty) ...[
              _buildAttachmentPreview(),
              const SizedBox(height: 24),
            ],
          ],
        ),
      ),
    );
  }

  void showFullScreenBottomDrawer(
      BuildContext context,
      VoidCallback onGuestAdded,
      String category,
      Map<String, dynamic> guestDetails,
      final ValueChanged<Map<String, dynamic>> onCallback,
      String fileUrl) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return FractionallySizedBox(
          heightFactor: 0.92,
          child: BlocProvider(
            create: (context) => AlertCalenderControllerBloc(),
            child: GuestFormEditDrawer(
                onGuestAdded: onGuestAdded,
                category: category,
                guestDetails: guestDetails,
                onCallback: onCallback,
                fileUrl: fileUrl),
          ),
        );
      },
    );
  }

  Widget _buildAttachmentPreview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.attachments,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              _buildFilePreview(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFilePreview() {
    final extension = fileUrl.split('.').last.toLowerCase();
    final isPDF = extension == 'pdf';
    final isImage = ['jpg', 'jpeg', 'png', 'gif'].contains(extension);

    if (isPDF) {
      return _buildPDFPreview();
    } else if (isImage) {
      return _buildImagePreview();
    } else {
      return _buildUnsupportedFileType();
    }
  }

  Widget _buildPDFPreview() {
    return Container(
      height: 200,
      width: double.infinity,
      child: FutureBuilder<String>(
        future: _downloadAndSavePDF(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(
              child: Text('Error loading PDF: ${snapshot.error}'),
            );
          }
          if (snapshot.hasData) {
            return Stack(
              children: [
                PDFView(
                  filePath: snapshot.data!,
                  enableSwipe: true,
                  swipeHorizontal: false,
                  autoSpacing: true,
                  pageSnap: true,
                  fitPolicy: FitPolicy.BOTH,
                ),
                Positioned(
                  right: 8,
                  top: 8,
                  child: IconButton(
                    icon: Icon(Icons.fullscreen, color: Colors.blue.shade400),
                    onPressed: () => _showFullScreenPDF(snapshot.data!),
                  ),
                ),
              ],
            );
          }
          return const Center(child: Text('No PDF preview available'));
        },
      ),
    );
  }

  Widget _buildImagePreview() {
    return Container(
      height: 200,
      width: double.infinity,
      child: Stack(
        children: [
          Image.network(
            fileUrl,
            fit: BoxFit.contain,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return Center(
                child: Text('Error loading image: $error'),
              );
            },
          ),
          Positioned(
            right: 8,
            top: 8,
            child: IconButton(
              icon: Icon(Icons.fullscreen, color: Colors.blue.shade400),
              onPressed: () => _showFullScreenImage(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnsupportedFileType() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Row(
        children: [
          Icon(Icons.file_present, color: Colors.grey),
          SizedBox(width: 8),
          Text(
            'Unsupported file type',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Future<String> _downloadAndSavePDF() async {
    final dio = Dio();
    final dir = await getApplicationDocumentsDirectory();
    final fileName = fileUrl.split('/').last;
    final filePath = '${dir.path}/$fileName';

    // Check if file already exists
    final file = File(filePath);
    if (await file.exists()) {
      return filePath;
    }

    try {
      await dio.download(
        fileUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = (received / total * 100).toStringAsFixed(0);
           
            // You can implement a progress indicator here if needed
          }
        },
      );
      return filePath;
    } catch (e) {
     
      throw Exception('Failed to download file: $e');
    }
  }

  void _showFullScreenPDF(String filePath) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('PDF Viewer'),
            backgroundColor: Colors.blue.shade400,
          ),
          body: PDFView(
            filePath: filePath,
            enableSwipe: true,
            swipeHorizontal: false,
            autoSpacing: true,
            pageSnap: true,
            fitPolicy: FitPolicy.BOTH,
          ),
        ),
      ),
    );
  }

  void _showFullScreenImage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('Image Viewer'),
            backgroundColor: Colors.blue.shade400,
          ),
          body: Center(
            child: InteractiveViewer(
              panEnabled: true,
              boundaryMargin: const EdgeInsets.all(20),
              minScale: 0.5,
              maxScale: 4,
              child: Image.network(
                fileUrl,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Avatar or User Icon
                  CircleAvatar(
                    radius: 16, // Adjust size as needed
                    backgroundColor: Colors.blue.shade400,
                    child: const Icon(
                      Icons.person,
                      color: Colors.white,
                      size: 20, // Adjust icon size
                    ),
                  ),
                  const SizedBox(width: 8), // Spacing between avatar and name
                  // Guest Name
                  Text(
                    guestData['name'],
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.location_on_outlined,
                      size: 18, color: Colors.blue.shade400),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '${guestData['invitedPlace']} - ${guestData['placeName']}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFeatures() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildFeatureItem(
              Icons.category_outlined,
              AppLocalizations.of(context)!.category,
              guestData['category'] == 'Individual'
                  ? AppLocalizations.of(context)!.individual
                  : AppLocalizations.of(context)!.group),
          _buildFeatureItem(Icons.access_time_outlined,
              AppLocalizations.of(context)!.time, guestData['invitedTime']),
          _buildFeatureItem(Icons.calendar_today_outlined,
              AppLocalizations.of(context)!.date, guestData['invitedDate']),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String value) {
    return Column(
      children: [
        Icon(icon, color: Colors.blue.shade400),
        const SizedBox(height: 8),
        Text(
          title,
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.contactInformation,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              _buildDetailRow(Icons.phone_outlined,
                  AppLocalizations.of(context)!.phone, guestData['phone']),
              const Divider(height: 20),
              _buildDetailRow(Icons.verified_outlined,
                  AppLocalizations.of(context)!.status, guestData['status']),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.additionalInformation,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              if (guestData['isMoreThanOneDay'] == 1)
                _buildDetailRow(
                    Icons.calendar_month,
                    AppLocalizations.of(context)!.numberOfDays,
                    guestData['numberOfDays'].toString()),
              if (guestData['isMoreThanOneDay'] == 1) const Divider(height: 20),
              if (guestData['haveCar'] == 1)
                _buildDetailRow(
                    Icons.directions_car_outlined,
                    AppLocalizations.of(context)!.carPlate,
                    guestData['car_plate']),
              if (guestData['haveCar'] == 1) const Divider(height: 20),
              if (guestData['haveItem'] == 1)
                _buildDetailRow(
                    Icons.inventory_2_outlined,
                    AppLocalizations.of(context)!.otherItems,
                    guestData['others']),
              if (guestData['haveItem'] == 1) const Divider(height: 20),
              if (guestData['haveLaptop'] == 1)
                _buildDetailRow(
                    Icons.laptop_mac_outlined,
                    AppLocalizations.of(context)!.laptopSerial,
                    guestData['serial_number']),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.blue.shade400),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Widget _buildScheduleSection() {
  //   return Container(
  //     padding: const EdgeInsets.all(16),
  //     decoration: BoxDecoration(
  //       color: Color.fromARGB(223, 244, 244, 245),
  //       borderRadius: BorderRadius.circular(16),
  //     ),
  //     child: InkWell(
  //       onTap: () {
  //         showModalBottomSheet(
  //           context: context,
  //           shape: const RoundedRectangleBorder(
  //             borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
  //           ),
  //           builder: (context) => Container(
  //             height: 400,
  //             padding: const EdgeInsets.all(16),
  //             child: Column(
  //               children: [
  //                 Text(
  //                   AppLocalizations.of(context)!.selectPerson,
  //                   style: TextStyle(
  //                     fontSize: 18,
  //                     fontWeight: FontWeight.bold,
  //                   ),
  //                 ),
  //                 const SizedBox(height: 16),
  //                 Expanded(
  //                   child: ListView.builder(
  //                     itemCount: peopleList.length,
  //                     itemBuilder: (context, index) {
  //                       final person = peopleList[index];
  //                       return ListTile(
  //                         title: Text(person['name'] ?? 'No Name'),
  //                         subtitle: Text(person['phone'] ?? 'No Phone'),
  //                         onTap: () {
  //                           setState(() {
  //                             selectedName = person['name'];
  //                             selectedPhone = person['phone'];
  //                             selectedId = person['id'];
  //                           });
  //                           Navigator.pop(context);
  //                         },
  //                       );
  //                     },
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //         );
  //       },
  //       child: Row(
  //         children: [
  //           Container(
  //             padding: const EdgeInsets.all(12),
  //             decoration: BoxDecoration(
  //               color: Colors.white,
  //               borderRadius: BorderRadius.circular(12),
  //             ),
  //             child: Icon(Icons.person, color: Colors.blue.shade400),
  //           ),
  //           const SizedBox(width: 16),
  //           Expanded(
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Text(
  //                   selectedName ?? AppLocalizations.of(context)!.selectPerson,
  //                   style: const TextStyle(
  //                     fontWeight: FontWeight.bold,
  //                     fontSize: 16,
  //                   ),
  //                 ),
  //                 if (selectedPhone != null)
  //                   Text(
  //                     selectedPhone!,
  //                     style: TextStyle(
  //                       color: Colors.grey.shade700,
  //                     ),
  //                   ),
  //               ],
  //             ),
  //           ),
  //           Container(
  //             decoration: BoxDecoration(
  //               color: Colors.blue.shade400,
  //               borderRadius: BorderRadius.circular(12),
  //             ),
  //             child: IconButton(
  //               icon: const Icon(Icons.send, color: Colors.white),
  //               onPressed: selectedName != null
  //                   ? () {
  //                       _isLoading ? null : _handleSubmit();
  //                     }
  //                   : null,
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // Widget _buildBottomBar(BuildContext context) {
  //   return AnimatedContainer(
  //     duration: const Duration(milliseconds: 300),
  //     curve: Curves.easeInOut,
  //     margin: const EdgeInsets.only(bottom: 8), // Reduced bottom margin
  //     width: double.infinity,
  //     padding: const EdgeInsets.all(16),
  //     decoration: BoxDecoration(
  //       color: Colors.transparent,
  //       borderRadius: BorderRadius.circular(16),
  //     ),
  //     child: _buildScheduleSection(),
  //   );
  // }
  Widget _buildScheduleSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9), // Semi-transparent white
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05), // Lighter shadow
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            builder: (context) => SizedBox(
              height: MediaQuery.of(context).size.height * 0.8,
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      AppLocalizations.of(context)!.selectPerson,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: AppLocalizations.of(context)!.search,
                        prefixIcon:
                            Icon(Icons.search, color: Colors.grey.shade500),
                        filled: true,
                        fillColor: Colors.grey.shade100,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                      onChanged: (value) {
                        // Add search/filter functionality
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: peopleList.length,
                      itemBuilder: (context, index) {
                        final person = peopleList[index];
                        return ListTile(
                          leading: CircleAvatar(
                            backgroundColor: Colors.blue.shade100,
                            child:
                                Icon(Icons.person, color: Colors.blue.shade600),
                          ),
                          title: Text(
                            person['name'] ?? 'No Name',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          subtitle: Text(person['phone'] ?? 'No Phone'),
                          trailing: selectedId == person['id']
                              ? Icon(Icons.check_circle,
                                  color: Theme.of(context).primaryColor)
                              : null,
                          onTap: () {
                            setState(() {
                              selectedName = person['name'];
                              selectedPhone = person['phone'];
                              selectedId = person['id'];
                            });
                            Navigator.pop(context);
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.person_outline,
                color: Theme.of(context).primaryColor,
                size: 28,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    selectedName ?? AppLocalizations.of(context)!.selectPerson,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: selectedName == null
                          ? Colors.grey.shade500
                          : Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                  ),
                  if (selectedPhone != null)
                    Text(
                      selectedPhone!,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                color: selectedName != null
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Icon(Icons.send, color: Colors.white),
                tooltip: selectedName == null
                    ? AppLocalizations.of(context)!.selectPersonFirst
                    : AppLocalizations.of(context)!.send,
                onPressed: (selectedName != null && !_isLoading)
                    ? () => _handleSubmit()
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: const EdgeInsets.all(16),
      constraints: BoxConstraints(
        // Add constraints to prevent overflow
        maxHeight: MediaQuery.of(context).size.height * 0.5, // Adjust as needed
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 2,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1.5,
              ),
              gradient: LinearGradient(
                colors: [
                  Colors.white.withOpacity(0.15),
                  Colors.white.withOpacity(0.05)
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: SingleChildScrollView(
              // Directly use SingleChildScrollView
              child: _buildScheduleSection(),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }
}
