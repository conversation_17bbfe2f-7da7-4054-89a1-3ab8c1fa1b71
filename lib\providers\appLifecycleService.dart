import 'dart:async';
import 'package:flutter/material.dart';
import 'package:guest_mobile_app/main.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppLifecycleService with WidgetsBindingObserver {
  static final AppLifecycleService _instance = AppLifecycleService._internal();

  factory AppLifecycleService() {
    return _instance;
  }

  AppLifecycleService._internal();

  Timer? _logoutTimer;

  void init() {
    WidgetsBinding.instance.addObserver(this);
  }

  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _logoutTimer?.cancel(); // Cancel timer if app closes
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.inactive || state == AppLifecycleState.paused) {
      _startLogoutTimer();
    } else if (state == AppLifecycleState.resumed) {
      _cancelLogoutTimer();
    }
  }

  void _startLogoutTimer() {
    _logoutTimer?.cancel(); // Cancel existing timer
    _logoutTimer = Timer(Duration(minutes: 5), () {
      _logoutUser();
    });
  }

  void _cancelLogoutTimer() {
    _logoutTimer?.cancel();
  }

  Future<void> _logoutUser() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.clear();
    navigatorKey.currentState?.pushNamedAndRemoveUntil('/login', (route) => false);
  }
}
