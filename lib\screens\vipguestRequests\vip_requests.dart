import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:guest_mobile_app/controller/alert_calender_bloc/alert_calender_controller_bloc.dart';
import 'package:guest_mobile_app/core/services/auth_service.dart';
import 'package:guest_mobile_app/core/services/viprequests_service.dart';
import 'package:guest_mobile_app/core/utils/tokenManager.dart';
import 'package:guest_mobile_app/screens/requests/request_details.dart';
import 'package:guest_mobile_app/widgets/custom_dialog.dart';
import 'package:guest_mobile_app/widgets/custom_drawer.dart';
import 'package:guest_mobile_app/widgets/guest_from_drawer.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'dart:async';

class VipRequestsScreen extends StatefulWidget {
  const VipRequestsScreen({Key? key}) : super(key: key);

  @override
  State<VipRequestsScreen> createState() => _VipRequestsScreenState();
}

class _VipRequestsScreenState extends State<VipRequestsScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  List<Map<String, dynamic>> guests = [];
  bool _isLoading = true;
  Timer? _debounceTimer;
  Map<String, dynamic>? _profileData;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _fetchGuests();
    _fetchProfile();
    // Add listener to search controller
    _searchController.addListener(_onSearchChanged);
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.35),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.65, curve: Curves.easeOut),
    ));

    _animationController.forward();
  }

  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) {
      _debounceTimer!.cancel();
    }
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _fetchGuests(isSearch: true); // Add isSearch parameter here
    });
  }

  String _formatTime(String timeString) {
    try {
      DateTime parsedTime = DateTime.parse(timeString);
      return DateFormat("MM/dd/yyyy hh:mm a").format(parsedTime);
    } catch (e) {
      
      return timeString; // Return the original string if parsing fails
    }
  }

  Future<void> _fetchGuests({bool isSearch = false}) async {
    if (!isSearch) {
      setState(() => _isLoading = true);
    }

    try {
      String? token = await TokenManager.getToken();
      final requestsService = VipRequestsService();
      final response =
          await requestsService.getRequests(token, _searchController.text);

      if (response['data'] != null && response['data'] is List) {
        _processGuestsData(response['data']);
      } else {
        throw Exception('Invalid data format');
      }
    } catch (e) {
     
    } finally {
      if (!isSearch) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _processGuestsData(List data) {
    final statusLabels = [
      '',
      AppLocalizations.of(context)!.draft,
      AppLocalizations.of(context)!.onprocess,
      AppLocalizations.of(context)!.approve_l,
      AppLocalizations.of(context)!.reject_l,
      AppLocalizations.of(context)!.entered_tx,
      AppLocalizations.of(context)!.canceled,
    ];

    setState(() {
      guests = sortByStatus(data.map((guest) {
        String rawTime = guest['sentAt'] ?? '14:00';

        return {
          'id': guest['id'] ?? '0',
          'name': guest['fullName'] ?? 'Unknown',
          'phone': guest['phoneNumber'] ?? '****** 567 890',
          'sentAt': _formatTime(rawTime),
          'avatarColor': Colors.blue,
          'senderName': guest['sentby'] ?? '',
          'status': statusLabels[guest['status'] ?? 0],
          'status_value': guest['status'] ?? 0,
        };
      }).toList());
    });
  }

  Future<void> _approveRequest(int? requestId) async {
    try {
      String? token = await TokenManager.getToken();
      final requestsService = VipRequestsService();
      await requestsService.saveApproveChanges(token, requestId);
      _fetchGuests();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.success),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.failed),
          backgroundColor: const Color.fromARGB(255, 175, 76, 76),
        ),
      );
    }
  }

  Future<void> _rejectRequest(int? requestId) async {
    try {
      String? token = await TokenManager.getToken();
      final requestsService = VipRequestsService();
      await requestsService.saveRejectChanges(token, requestId);
      _fetchGuests();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.success),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.failed),
          backgroundColor: const Color.fromARGB(255, 175, 76, 76),
        ),
      );
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> sortByStatus(List<Map<String, dynamic>> arr) {
    arr.sort((a, b) {
      int statusA = a['status_value'] ?? 0;
      int statusB = b['status_value'] ?? 0;

      if (statusA == 0 && statusB != 0) return 1; // Move 'a' down
      if (statusB == 0 && statusA != 0) return -1; // Move 'b' down

      return statusA.compareTo(statusB); // Regular sorting otherwise
    });
    return arr;
  }

  Future<void> _fetchProfile() async {
    try {
      final authService = AuthService();
      final profileData = await authService.getProfile();

      setState(() {
        _profileData = profileData['data'];
        _isLoading = false;
      });
    } catch (e) {
     
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey, // Set the GlobalKey here
      backgroundColor: const Color(0xFFF8F9FE),
      appBar: _buildAppBar(),
      drawer: _isLoading
          ? const Drawer(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          : CustomDrawer(profileData: _profileData ?? {}),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Padding(
              padding: const EdgeInsets.only(top: 16.0), // Add top margin
              child: Column(
                children: [
                  _buildSearchBar(),
                  Expanded(
                    child: _isLoading
                        ? _buildLoadingIndicator()
                        : _buildGuestList(),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      leading: IconButton(
        icon: const Icon(Icons.menu, color: Color(0xFF4A4A4A)),
        onPressed: () {
          _scaffoldKey.currentState?.openDrawer();
        },
      ),
      title: Text(
        AppLocalizations.of(context)!.vipRequests,
        style: const TextStyle(
          fontSize: 20,
          color: Color(0xFF4A4A4A),
          fontWeight: FontWeight.w500,
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.more_horiz, color: Color(0xFF4A4A4A)),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
        color: Colors.white,
      ),
      child: Row(
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child: Icon(Icons.search, color: Color(0xFF9E9E9E), size: 20),
          ),
          Expanded(
            child: TextField(
              controller: _searchController,
              style: const TextStyle(
                fontSize: 15,
                color: Color(0xFF4A4A4A),
              ),
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)!.searchGuests,
                hintStyle: const TextStyle(
                  color: Color(0xFF9E9E9E),
                  fontSize: 15,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          if (_searchController.text.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.close, color: Color(0xFF9E9E9E), size: 20),
              onPressed: () {
                _searchController.clear();
                _fetchGuests();
              },
            ),
        ],
      ),
    );
  }

  Widget _buildGuestList() {
    if (guests.isEmpty) {
      return Center(
        child: Text(AppLocalizations.of(context)!.noGuestsFound),
      );
    }

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: guests.length,
          itemBuilder: (context, index) {
            return _buildGuestCard(guests[index], index);
          },
        ),
      ),
    );
  }

  Widget _buildGuestCard(Map<String, dynamic> guest, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.1)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {},
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Stack(
                        children: [
                          CircleAvatar(
                            radius: 24,
                            backgroundColor:
                                guest['avatarColor'].withOpacity(0.8),
                            child: Text(
                              guest['name'][0],
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              guest['name'],
                              style: const TextStyle(
                                fontSize: 16,
                                color: Color(0xFF4A4A4A),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              guest['phone'],
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      ),
                      _buildStatusChip(guest['status_value']),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildDetailsSection(guest),
                  if (guest['status_value'] != 4)
                    Padding(
                      padding: const EdgeInsets.only(top: 12.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          if (guest['status_value'] != 2) ...[
                            ElevatedButton(
                              onPressed: () =>
                                  showApproveConfirmation(context, guest['id']),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 10),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                AppLocalizations.of(context)!.approve_l,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                          ],
                          const SizedBox(width: 10),
                          if (guest['status_value'] != 3) ...[
                            ElevatedButton(
                              onPressed: () =>
                                  showRejectConfirmation(context, guest['id']),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 10),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                AppLocalizations.of(context)!.reject_l,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void showApproveConfirmation(BuildContext context, int requestId) {
    Navigator.of(context).push(CustomDialogRoute(
      builder: (context) => Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.check_circle_outline,
                    size: 48,
                    color: Colors.green,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.confirmApprove,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    AppLocalizations.of(context)!.approveMessage,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.cancel,
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _approveRequest(requestId);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.approve,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }

  void showRejectConfirmation(BuildContext context, int? requestId) {
    Navigator.of(context).push(CustomDialogRoute(
      builder: (context) => Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.check_circle_outline,
                    size: 48,
                    color: Color.fromARGB(255, 175, 76, 76),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.confirmReject,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    AppLocalizations.of(context)!.rejectMessage,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.cancel,
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _rejectRequest(requestId);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              const Color.fromARGB(255, 175, 76, 76),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.reject,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }

  Widget _buildStatusChip(int status) {
    // Define status mappings
    final Map<int, String> statusLabels = {
      1: AppLocalizations.of(context)!.onprocess,
      2: AppLocalizations.of(context)!.approved_tx,
      3: AppLocalizations.of(context)!.rejected_tx,
      4: AppLocalizations.of(context)!.entered_tx,
    };

    final Map<int, Color> statusColors = {
      1: const Color(0xFFFFF3E0), // Pending - Light Orange
      2: const Color(0xFFE8F5E9), // Approved - Light Green
      3: const Color(0xFFFFEBEE),
      4: const Color(0xFFF3E5F5) // Rejected - Light Purple
    };

    final Map<int, Color> textColors = {
      1: const Color(0xFFF57C00), // Pending - Dark Orange
      2: const Color(0xFF2E7D32), // Approved - Dark Green
      3: const Color(0xFFD32F2F), // Rejected - Dark Purple
      4: const Color(0xFF6A1B9A), // Entered - Dark Red
    };

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: statusColors[status] ?? Colors.grey[200], // Default Light Grey
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        statusLabels[status] ?? 'Unknown', // Default Text
        style: TextStyle(
          fontSize: 13,
          color: textColors[status] ?? Colors.black, // Default Black
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildDetailsSection(Map<String, dynamic> guest) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FE),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildDetailRow(
            AppLocalizations.of(context)!.sentAt,
            '${guest['sentAt']}',
            Icons.punch_clock,
          ),
          _buildDetailRow(
            AppLocalizations.of(context)!.sentBy,
            '${guest['senderName']}',
            Icons.supervised_user_circle,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: const Color(0xFF9E9E9E)),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  color: Color(0xFF9E9E9E),
                  fontSize: 12,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Color(0xFF4A4A4A),
                  fontSize: 13,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
