import 'package:dio/dio.dart';
import 'package:guest_mobile_app/core/utils/apiClient.dart';

class GuestService {
  final ApiClient apiClient = ApiClient();

  Future<Map<String, dynamic>> getGuests(
      String? token, String query, int type) async {
    try {
      final response = await apiClient.dio.post(
        type == 1 ? '/guests/request' : '/guests/group/request',
        data: {
          "query": query,
          "skip": 0,
          "step": 20,
        },
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data['data']['data'] ?? [],
          'total': response.data['total'] ?? 0,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error occurred',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'An unexpected error occurred',
      };
    }
  }

  Future<Map<String, dynamic>> getGuestById(
      String? token, int id, int type) async {
    try {
      final response = await apiClient.dio.get(
        type == 1 ? '/guest/$id' : '/group/guest/$id',
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data['data'] ?? [],
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error occurred',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'An unexpected error occurred',
      };
    }
  }

  Future<Map<String, dynamic>> getInvitingPlaces(String? token) async {
    try {
      final response = await apiClient.dio.get(
        '/invitation/area',
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        // Directly return response.data
        return {
          'success': true,
          'data': response.data['data'],
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error occurred',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'An unexpected error occurred',
      };
    }
  }

  Future<Map<String, dynamic>> getPlacesName(String? token, placeId) async {
    try {
      final response = await apiClient.dio.get(
        '/invitation/places/$placeId',
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        // Directly return response.data
        return {
          'success': true,
          'data': response.data['data'],
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error occurred',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'An unexpected error occurred',
      };
    }
  }

  Future<Map<String, dynamic>> getAllPlacesName(String? token) async {
    try {
      final response = await apiClient.dio.get(
        '/invitation/places',
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        // Directly return response.data
        return {
          'success': true,
          'data': response.data['data'],
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error occurred',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'An unexpected error occurred',
      };
    }
  }

  Future<Map<String, dynamic>> saveGuest(
      String? token, Map<String, dynamic> guestData, int type) async {
    try {
      // Create FormData if a file exists in guestData

      FormData formData;
      if (guestData.containsKey('file')) {
        formData = FormData.fromMap(guestData);
      } else {
        formData = FormData.fromMap(guestData);
      }

      final response = await apiClient.dio.post(
          type == 1
              ? '/v2/create/guests/request'
              : '/v2/create/guests/group/request',
          data: type == 1 ? guestData : formData,
          options: type == 2
              ? Options(
                  headers: {
                    'Authorization': 'Bearer $token',
                    'Content-Type': 'multipart/form-data',
                  },
                )
              : Options(
                  headers: {
                    'Authorization': 'Bearer $token',
                    'Content-Type': 'application/json',
                  },
                ));

      // Check if the request was successful
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;

        return {
          'success': true,
          'message': responseData['message'] ?? 'Guest saved successfully!',
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Failed to save guest',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error: $e',
      };
    }
  }

  Future<Map<String, dynamic>> updateGuest(
      String? token, Map<String, dynamic> guestData, int type, int id) async {
    try {
      // Create FormData if a file exists in guestData

      FormData formData;
      if (guestData.containsKey('file')) {
        formData = FormData.fromMap(guestData);
      } else {
        formData = FormData.fromMap(guestData);
      }

      final response = await apiClient.dio.put(
          type == 1
              ? '/mobile/individual/update/guests/request/$id'
              : '/mobile/update/guests/request/$id',
          data: type == 1 ? guestData : formData,
          options: type == 2
              ? Options(
                  headers: {
                    'Authorization': 'Bearer $token',
                    'Content-Type': 'multipart/form-data',
                  },
                )
              : Options(
                  headers: {
                    'Authorization': 'Bearer $token',
                    'Content-Type': 'application/json',
                  },
                ));

      // Check if the request was successful
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        return {
          'success': true,
          'message': responseData ?? 'Guest saved successfully!',
        };
      } else {
        return {
          'success': false,
          'message': response.data ?? 'Failed to save guest',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error: $e',
      };
    }
  }

  Future<Map<String, dynamic>> sendRequest(
      String? token, int? guestType, int? guestId, int? approverId) async {
    try {
      final response = await apiClient.dio.post(
        '/send/request',
        data: {
          "guest_type": guestType,
          "guestId": guestId,
          "approverId": approverId
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Check if the request was successful
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        return {
          'success': true,
          'message': responseData ?? 'send request successfully!',
        };
      } else {
        return {
          'success': false,
          'message': response.data ?? 'Failed to send request',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error: $e',
      };
    }
  }

  Future<Map<String, dynamic>> getApprover(String? token) async {
    try {
      final response = await apiClient.dio.get(
        '/approver',
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data['data'] ?? [],
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error occurred',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'An unexpected error occurred',
      };
    }
  }

  Future<Map<String, dynamic>> getAttachments(String? token, int id) async {
    try {
      final response = await apiClient.dio.get(
        '/guests/group/others/details/request/$id',
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data['data'] ?? [],
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error occurred',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'An unexpected error occurred',
      };
    }
  }

  Future<Map<String, dynamic>> saveCancelChanges(
      String? token, int? id, int? type) async {
    try {
      final response = await apiClient.dio.put(
        type == 1
            ? '/mobile/requests/cancel/$id'
            : '/mobile/requests/group/cancel/$id',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Check if the request was successful
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        return {
          'success': true,
          'message': responseData ?? 'successfully make changes!',
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Failed to make changes',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error: $e',
      };
    }
  }

  Future<Map<String, dynamic>> saveApproveChanges(
      String? token, int? id, int type) async {
    try {
      final response = await apiClient.dio.put(
        type == 1
            ? '/mobile/requests/approve/head/$id'
            : '/mobile/requests/group/approve/head/$id',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Check if the request was successful
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        return {
          'success': true,
          'message': responseData ?? 'successfully make changes!',
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Failed to make changes',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error: $e',
      };
    }
  }
}
