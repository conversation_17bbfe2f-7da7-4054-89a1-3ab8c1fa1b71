import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:animate_do/animate_do.dart';
import 'package:guest_mobile_app/controller/alert_calender_bloc/alert_calender_controller_bloc.dart';
import 'package:guest_mobile_app/core/constants/constants.dart';
import 'package:guest_mobile_app/core/services/guests_service.dart';
import 'package:guest_mobile_app/core/utils/tokenManager.dart';
import 'package:guest_mobile_app/widgets/date_picker_alret.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:http_parser/http_parser.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart'
    show getApplicationDocumentsDirectory;

enum AttachmentType { none, upload, capture }

class GuestFormEditDrawer extends StatefulWidget {
  final VoidCallback onGuestAdded;
  final String category;
  final Map<String, dynamic> guestDetails;
  final String fileUrl;
  final ValueChanged<Map<String, dynamic>> onCallback;
  const GuestFormEditDrawer(
      {required this.onGuestAdded,
      required this.category,
      required this.guestDetails,
      required this.onCallback,
      required this.fileUrl});
  @override
  _GuestFormDrawerState createState() => _GuestFormDrawerState();
}

class _GuestFormDrawerState extends State<GuestFormEditDrawer> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  DateTime? selectedDate;
  TimeOfDay? selectedTime;
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  List<Map<String, dynamic>> invitedPlaces = [];
  List<Map<String, dynamic>> PlaceNames = [];
  List<Map<String, dynamic>> AllPlaceNames = [];
  Map<String, dynamic>? selectedPlace;
  Map<String, dynamic>? selectedPlaceName;
  bool _hasCar = false;
  bool _hasLaptop = false;
  bool _hasOtherItems = false;
  bool _isMoreThanOneDay = false;
  bool _isDownloadingPDF = false;
  String? _localPDFPath;

  final ImagePicker _picker = ImagePicker();
  File? _attachmentFile; // File to be uploaded (PDF, image, etc.)
  late AttachmentType _attachmentType = AttachmentType.none;
  bool _isCapture = false;
  // Controllers for input fields
  final TextEditingController _carPlateController = TextEditingController();
  final TextEditingController _otherItemsController = TextEditingController();
  final TextEditingController _laptopSerialController = TextEditingController();
  final TextEditingController _numberOfDaysController = TextEditingController();
  // Form key for validation
  late Map<String, dynamic> guestData;
  late Map<String, dynamic> guestList;
  final bool isHead = false;
  String? existingImageUrl;
  bool removeExistingImage = false;
  void initState() {
    super.initState();
    guestData = Map<String, dynamic>.from(widget.guestDetails);
    existingImageUrl = widget.fileUrl;

    _getInvitingPlaces();

    _getAllPlacesName();
  }

  Future<void> _fetchGuest() async {
    try {
      String? token = await TokenManager.getToken();
      final guestService = GuestService();
      final response = await guestService.getGuestById(token, guestData['id'],
          guestData['category'] == 'Individual' ? 1 : 2);

      if (response['data'] != null) {
        _processGuestsData(response['data']);
      } else {
        throw Exception('Invalid data format');
      }
    } catch (e) {
      
    } finally {}
  }

  String _formatTime(String timeString) {
    try {
      DateTime parsedTime = DateTime.parse(timeString);
      return DateFormat.jm().format(parsedTime);
    } catch (e) {
     
      return timeString;
    }
  }

  void _processGuestsData(Map<String, dynamic> data) {
    const statusLabels = [
      '',
      'Draft',
      'On process',
      'Approved',
      'Rejected',
      'Canceled'
    ];
    String rawTime = data['invitedTime'] ?? '14:00';

    guestData['category'] == 'Individual'
        ? guestList = {
            'id': data['id'] ?? '0',
            'name': data['fullName'] ?? 'Unknown',
            'phone': data['phoneNumber'] ?? '****** 567 890',
            'invitedPlace': data['placeCategory']['name'] ?? 'Conference Hall',
            'placeName': data['place']['name'] ?? 'ሁሉም',
            'status': statusLabels[data['status'] ?? 1],
            'status_value': data['status'] ?? 0,
            'invitedDate': data['invitedDate'] ?? '2024-06-30',
            'invitedTime': _formatTime(rawTime),
            'avatarColor': Colors.blue,
            'category': guestData['category'],
            'haveCar': data['haveCar'] ?? 0,
            'haveItem': data['haveItem'] ?? 0,
            'haveLaptop': data['haveLaptop'] ?? 0,
            'serial_number': data['serial_number'] ?? '',
            'others': data['others'] ?? '',
            'car_plate': data['car_plate'] ?? '',
            'isHead': guestData['isHead'] || false,
            'placeCategory': data['placeCategory'] ?? 0,
            'place': data['place'] ?? 0,
            'isMoreThanOneDay': data['isMoreThanOneDay'] ?? 0,
            'numberOfDays': data['numberOfDays'] ?? 0,
          }
        : guestList = {
            'id': data['id'] ?? '0',
            'name': data['fullName'] ?? 'Unknown',
            'phone': data['phoneNumber'] ?? '****** 567 890',
            'invitedPlace': data['placeCategory']['name'] ?? 'Conference Hall',
            'placeName': data['place']['name'] ?? 'ሁሉም',
            'status': statusLabels[data['status'] ?? 1],
            'status_value': data['status'] ?? 0,
            'invitedDate': data['invitedDate'] ?? '2024-06-30',
            'invitedTime': _formatTime(rawTime),
            'avatarColor': Colors.blue,
            'category': guestData['category'],
            'isHead': guestData['isHead'] || false,
            'placeCategory': data['placeCategory'] ?? 0,
            'place': data['place'] ?? 0,
            'fileUrl': AppConstants.apiBaseIP + "/" + data['filePath'],
            'isMoreThanOneDay': data['isMoreThanOneDay'] ?? 0,
            'numberOfDays': data['numberOfDays'] ?? 0,
          };

    widget.onCallback(guestList);
  }

  void _fetchDetails() async {
    if (guestData['category'] == 'Individual') {
      nameController.text = guestData['name'] ?? '';
      phoneController.text = guestData['phone'] ?? '';
      selectedPlace = invitedPlaces.firstWhere(
        (place) => place['name'] == guestData['invitedPlace'],
        orElse: () => {},
      );
      await _getPlacesName(selectedPlace?['id']);
      selectedPlaceName = PlaceNames.firstWhere(
        (place) => place['name'] == guestData['placeName'],
        orElse: () => {},
      );

      _hasCar = guestData['haveCar'] == 1 ? true : false;
      _carPlateController.text = guestData['car_plate'] ?? '';
      _hasLaptop = guestData['haveLaptop'] == 1 ? true : false;
      _laptopSerialController.text = guestData['serial_number'] ?? '';
      _hasOtherItems = guestData['haveItem'] == 1 ? true : false;
      _otherItemsController.text = guestData['others'] ?? '';
      _isMoreThanOneDay = guestData['isMoreThanOneDay'] == 1 ? true : false;
      _numberOfDaysController.text = guestData['numberOfDays'].toString() ?? '';

      if (guestData['invitedDate'] != null) {
        String invitedDate = guestData['invitedDate']; // e.g., "16/05/2017"

        invitedDate = invitedDate.replaceAll('/', '-'); // Replace '/' with '-'

        try {
          // Define the format based on your date string
          DateFormat inputFormat =
              DateFormat('dd-MM-yyyy'); // Match the format "16-05-2017"
          DateTime date = inputFormat.parse(invitedDate);
          selectedDate = date;
        } catch (e) {
          
        }
      }

      if (guestData['invitedTime'] != null) {
        String invitedTime = guestData['invitedTime']; // e.g., "2:54 PM"

        // Replace non-breaking spaces with regular spaces
        invitedTime =
            invitedTime.replaceAll('\u202F', ' ').replaceAll('\u00A0', ' ');

        // Combine with a dummy date for parsing
        String combinedDateTime = "2000-01-01 $invitedTime";

        // Use DateFormat to parse the string
        DateFormat dateFormat = DateFormat("yyyy-MM-dd h:mm a");

        try {
          DateTime dateTime =
              dateFormat.parse(combinedDateTime); // Parse using intl
          selectedTime = TimeOfDay.fromDateTime(dateTime);

        
        } catch (e) {
        
        }
      }
    } else {
      nameController.text = guestData['name'] ?? '';
      phoneController.text = guestData['phone'] ?? '';
      _isMoreThanOneDay = guestData['isMoreThanOneDay'] == 1 ? true : false;
      _numberOfDaysController.text = guestData['numberOfDays'].toString() ?? '';
      selectedPlace = invitedPlaces.firstWhere(
        (place) => place['name'] == guestData['invitedPlace'],
        orElse: () => <String, dynamic>{},
      );
      await _getPlacesName(selectedPlace?['id']);

      selectedPlaceName = PlaceNames.firstWhere(
        (place) => place['name'] == guestData['placeName'],
        orElse: () => {},
      );

      if (guestData['invitedDate'] != null) {
        String invitedDate = guestData['invitedDate']; // e.g., "16/05/2017"

        invitedDate = invitedDate.replaceAll('/', '-'); // Replace '/' with '-'

        try {
          // Define the format based on your date string
          DateFormat inputFormat =
              DateFormat('dd-MM-yyyy'); // Match the format "16-05-2017"
          DateTime date = inputFormat.parse(invitedDate);
          selectedDate = date;
        } catch (e) {
          
        }
      }

      if (guestData['invitedTime'] != null) {
        String invitedTime = guestData['invitedTime']; // e.g., "2:54 PM"

        // Replace non-breaking spaces with regular spaces
        invitedTime =
            invitedTime.replaceAll('\u202F', ' ').replaceAll('\u00A0', ' ');

        // Combine with a dummy date for parsing
        String combinedDateTime = "2000-01-01 $invitedTime";

        // Use DateFormat to parse the string
        DateFormat dateFormat = DateFormat("yyyy-MM-dd h:mm a");

        try {
          DateTime dateTime =
              dateFormat.parse(combinedDateTime); // Parse using intl
          selectedTime = TimeOfDay.fromDateTime(dateTime);

        
        } catch (e) {
          
        }
      }
    }
  }

  Future<void> _getInvitingPlaces() async {
    setState(() => _isLoading = true);

    try {
      String? token = await TokenManager.getToken();
      final guestService = GuestService();
      final response = await guestService.getInvitingPlaces(token);

      if (response['success'] == true && response['data'] != null) {
        invitedPlaces = List<Map<String, dynamic>>.from(response['data']);

        _fetchDetails();
      } else {
        throw Exception(
            response['message'] ?? 'Failed to fetch inviting places');
      }
    } catch (e) {
      
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
    );

    if (result != null) {
      setState(() {
        _attachmentFile = File(result.files.single.path!);
      });
    }
  }

  Future<void> _requestPermissions() async {
    // Request camera permission
    var cameraStatus = await Permission.camera.request();

    // Handle different Android versions
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;

      if (sdkVersion >= 33) {
        // Android 13 (API 33) and above
        var photoStatus = await Permission.photos.request();
       
      } else {
        var storageStatus = await Permission.storage.request();
        
      }
    }

    // If the camera permission is granted, open the camera
    if (await Permission.camera.isGranted) {
      _captureDocumentImage();
    } else {
     
    }
  }

  Future<void> _captureDocumentImage() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.rear,
        imageQuality: 90,
      );

      if (pickedFile != null) {
        final File imageFile = File(pickedFile.path);
        final croppedFile = await _cropImage(imageFile);

        if (croppedFile != null) {
          setState(() {
            _attachmentFile = File(croppedFile.path);
            _attachmentType = AttachmentType.capture;
          });
        }
      }
    } on PlatformException catch (e) {
      _showCameraErrorDialog(e.message ?? 'Unknown camera error');
    }
  }

  void _showCameraErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Camera Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  // Add these methods for PDF handling
  Future<String> _downloadAndSavePDF(String url) async {
    setState(() => _isDownloadingPDF = true);
    final dio = Dio();
    final dir = await getApplicationDocumentsDirectory();
    final fileName = url.split('/').last;
    final filePath = '${dir.path}/$fileName';

    try {
      await dio.download(url, filePath);
      setState(() => _localPDFPath = filePath);
    } catch (e) {
     
    } finally {
      setState(() => _isDownloadingPDF = false);
    }
    return filePath;
  }

  void _showFullScreenPDF(String url) async {
    final path = await _downloadAndSavePDF(url);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(title: Text('PDF Preview')),
          body: PDFView(filePath: path),
        ),
      ),
    );
  }

  Future<CroppedFile?> _cropImage(File imageFile) async {
    CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: imageFile.path,
      aspectRatioPresets: [
        CropAspectRatioPreset.original,
        CropAspectRatioPreset.ratio4x3,
        CropAspectRatioPreset.ratio16x9,
      ],
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Crop Image',
          toolbarColor: Colors.deepOrange,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        IOSUiSettings(
          title: 'Crop Image',
        ),
      ],
    );

    return croppedFile;
  }

  Future<void> _getAllPlacesName() async {
    setState(() => _isLoading = true);

    try {
      String? token = await TokenManager.getToken();
      final guestService = GuestService();
      final response = await guestService.getAllPlacesName(token);

      if (response['success'] == true && response['data'] != null) {
        // AllPlaceNames = List<Map<String, dynamic>>.from();
        mappingData(response['data']);
      } else {
        throw Exception(
            response['message'] ?? 'Failed to fetch inviting places');
      }
    } catch (e) {
     
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> mappingData(List data) async {
    setState(() {
      AllPlaceNames = data.map((user) {
        return {
          'id': user['id'] ?? '0',
          'name': user['name'] ?? 'Unknown',
          'areaId': user['areaId'] ?? '0',
        };
      }).toList();
    });
  }

  Future<void> _getPlacesName(placeId) async {
    setState(() => _isLoading = true);

    try {
      String? token = await TokenManager.getToken();
      final guestService = GuestService();
      final response = await guestService.getPlacesName(token, placeId);
     
      if (response['success'] == true && response['data'] != null) {
        PlaceNames = List<Map<String, dynamic>>.from(response['data']);
      } else {
        throw Exception(
            response['message'] ?? 'Failed to fetch inviting places');
      }
    } catch (e) {
      
    } finally {
      setState(() => _isLoading = false);
    }
  }

  IconData _getUpdatedIcon(IconData originalIcon) {
    // Map original icons to outlined variants
    final iconMap = {
      Icons.person: Icons.person_outline,
      Icons.phone: Icons.phone_outlined,
      Icons.event: Icons.event_outlined,
      Icons.calendar_today: Icons.calendar_today_outlined,
      Icons.access_time: Icons.access_time_outlined,
      Icons.directions_car: Icons.directions_car_outlined,
      Icons.laptop: Icons.laptop_outlined,
      Icons.list: Icons.list_outlined,
      Icons.attach_file: Icons.attach_file_outlined,
      Icons.camera_alt: Icons.camera_alt_outlined,
      Icons.upload_file: Icons.upload_file_outlined,
      Icons.calendar_month: Icons.calendar_month,
    };

    return iconMap[originalIcon] ?? originalIcon;
  }

  InputDecoration _buildInputDecoration(String label, IconData icon) {
    return InputDecoration(
      labelText: label,
      labelStyle: GoogleFonts.poppins(
        color: Colors.grey.shade400,
        fontSize: 14,
      ),
      prefixIcon: Icon(
        _getUpdatedIcon(icon),
        color: const Color(0xFF2B2D3E),
        size: 22,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: Color(0xFF2B2D3E)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: Color(0xFF2B2D3E)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: Color(0xFF2B2D3E), width: 2),
      ),
      filled: true,
      fillColor: const Color.fromARGB(255, 255, 255, 255),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(32)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FadeInDown(
            duration: const Duration(milliseconds: 300),
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 12),
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          Expanded(
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    FadeInDown(
                      duration: const Duration(milliseconds: 400),
                      child: Text(
                        AppLocalizations.of(context)!.addNewGuest,
                        style: GoogleFonts.poppins(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF2B2D3E),
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Form Fields
                    FadeInUp(
                      duration: const Duration(milliseconds: 500),
                      child: _buildFormFields(),
                    ),

                    const SizedBox(height: 32),

                    // Submit Button
                    FadeInUp(
                      duration: const Duration(milliseconds: 600),
                      child: _buildSubmitButton(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        // Name Input
        TextFormField(
          controller: nameController,
          decoration: _buildInputDecoration(
              widget.category == 'Individual'
                  ? AppLocalizations.of(context)!.guestName
                  : AppLocalizations.of(context)!.groupLeaderName,
              Icons.person),
          validator: (value) => value?.isEmpty ?? true
              ? AppLocalizations.of(context)!.vaildGuestName
              : null,
        ),
        const SizedBox(height: 20),

        // Phone Input
        TextFormField(
          controller: phoneController,
          decoration: _buildInputDecoration(
                  AppLocalizations.of(context)!.phone, Icons.phone)
              .copyWith(
            hintText: '09/07xxxxxxxx',
            helperText: AppLocalizations.of(context)!.format,
            errorMaxLines: 2,
          ),
          keyboardType: TextInputType.phone,
          inputFormatters: [
            PhoneNumberFormatter(),
            LengthLimitingTextInputFormatter(10),
          ],
          validator: _validatePhone,
          onChanged: (value) {
            // Optional: You can add real-time validation feedback here
            setState(() {});
          },
        ),
        const SizedBox(height: 20),

        // Event Type Dropdown
        _buildEventTypeDropdown(),
        const SizedBox(height: 20),
        if (selectedPlace != null && selectedPlace?['id'] != 6)
          AnimatedOpacity(
            duration: const Duration(milliseconds: 300),
            opacity: selectedPlace != null ? 1.0 : 0.0,
            child: _buildVenueDropdown(),
          ),
        const SizedBox(height: 20),
        // Additional fields for Individual category
        if (widget.category == 'Individual') ...[
          _buildHasCarCheckbox(),
          if (_hasCar) _buildCarPlateInput(),
          const SizedBox(height: 20),
          _buildHasLaptopCheckbox(),
          if (_hasLaptop) _buildLaptopSerialInput(),
          const SizedBox(height: 20),
          _buildHasOtherItemsCheckbox(),
          if (_hasOtherItems) _buildOtherItemsInput(),
        ],

        // Additional fields for Group category
        if (widget.category == 'Group') ...[
          const SizedBox(height: 10),
          _buildAttachmentOptions(),
          const SizedBox(height: 10),
        ],
        const SizedBox(height: 10),
        _buildMoreThanOneDayCheckbox(),
        if (_isMoreThanOneDay) _buildNumberOfDaysInput(),
        const SizedBox(height: 20),
        // Date and Time Selection
        _buildDateTimeSelection(),
      ],
    );
  }

  Widget _buildNumberOfDaysInput() {
    return FadeInUp(
        duration: Duration(milliseconds: 300),
        child: TextFormField(
          controller: _numberOfDaysController,
          decoration: _buildInputDecoration(
              AppLocalizations.of(context)!.numberOfDays, Icons.calendar_month),
          validator: (value) {
            if (_isMoreThanOneDay && (value == null || value.isEmpty)) {
              return AppLocalizations.of(context)!.validateNumberOfDays;
            }
            return null;
          },
        ));
  }

  Widget _buildMoreThanOneDayCheckbox() {
    return FadeInUp(
        duration: const Duration(milliseconds: 300),
        child: CheckboxListTile(
          title: Text(AppLocalizations.of(context)!.isMoreThanOneDays),
          value: _isMoreThanOneDay,
          onChanged: (value) {
            setState(() {
              _isMoreThanOneDay = value ?? false;
            });
          },
        ));
  }

  Widget _buildAttachmentOptions() {
    final existingFileUrl = widget.fileUrl;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.attach_file,
                color: Color(0xFF2B2D3E),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context)!.documentAttachment,
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Modern switch with custom design
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade200,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildOptionLabel(
                  icon: Icons.upload_file,
                  label: AppLocalizations.of(context)!.uploadFile,
                  isSelected: !_isCapture,
                ),
                Switch.adaptive(
                  value: _isCapture,
                  onChanged: (value) {
                    setState(() {
                      _isCapture = value;
                      _attachmentFile = null;
                      _attachmentType = AttachmentType.none;
                    });
                  },
                  activeColor: Theme.of(context).primaryColor,
                ),
                _buildOptionLabel(
                  icon: Icons.camera_alt,
                  label: AppLocalizations.of(context)!.capture,
                  isSelected: _isCapture,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // File Preview Section
          if (_attachmentFile != null) ...[
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.shade200,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.preview,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade800,
                          ),
                        ),
                        IconButton(
                          icon: Icon(Icons.close, color: Colors.grey.shade600),
                          onPressed: () {
                            setState(() {
                              _attachmentFile = null;
                              _attachmentType = AttachmentType.none;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                  _attachmentFile!.path.endsWith('.pdf')
                      ? Container(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              const Icon(Icons.picture_as_pdf,
                                  color: Colors.red, size: 32),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  _attachmentFile!.path.split('/').last,
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    color: Colors.grey.shade700,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        )
                      : Container(
                          height: 200,
                          width: double.infinity,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              _attachmentFile!,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ] else if (existingImageUrl != null && !removeExistingImage) ...[
            // Existing image preview
            _buildExistingFilePreview(existingFileUrl),
          ],
          if (removeExistingImage)
            Text(AppLocalizations.of(context)!.existingRemoved,
                style: const TextStyle(color: Colors.red)),
          // Upload/Capture button (existing code)

          // Action Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () async {
                if (_isCapture) {
                  await _requestPermissions();
                } else {
                  await _pickFile();
                }
              },
              icon: Icon(
                _isCapture ? Icons.camera_alt : Icons.upload_file,
                size: 20,
              ),
              label: Text(
                _isCapture
                    ? AppLocalizations.of(context)!.captureDocument
                    : AppLocalizations.of(context)!.chooseFile,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
                backgroundColor: Color(0xFF2B2D3E),
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExistingFilePreview(String url) {
    final isPDF = url.toLowerCase().endsWith('.pdf');

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [/*...*/],
      ),
      child: Column(
        children: [
          ListTile(
            leading: Icon(isPDF ? Icons.picture_as_pdf : Icons.image,
                color: Colors.red),
            title: Text('Existing ${isPDF ? 'PDF' : 'Image'}'),
            trailing: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => setState(() => removeExistingImage = true),
            ),
          ),
          if (isPDF)
            _buildPDFPreviewSection(url)
          else
            Image.network(url, height: 200, fit: BoxFit.cover),
          TextButton(
            onPressed: () =>
                isPDF ? _showFullScreenPDF(url) : _showFullScreenImage(url),
            child: const Text('FULL SCREEN PREVIEW'),
          ),
        ],
      ),
    );
  }

  Widget _buildPDFPreviewSection(String url) {
    return Column(
      children: [
        if (_isDownloadingPDF)
          const LinearProgressIndicator()
        else if (_localPDFPath != null)
          Container(
            height: 200,
            child: PDFView(filePath: _localPDFPath!),
          )
        else
          ElevatedButton(
            onPressed: () => _downloadAndSavePDF(url),
            child: Text('Load PDF Preview'),
          ),
      ],
    );
  }

  void _showFullScreenImage(String url) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(title: Text('Image Preview')),
          body: Center(child: Image.network(url)),
        ),
      ),
    );

    Widget _buildPDFPreviewSection(String url) {
      return Column(
        children: [
          if (_isDownloadingPDF)
            const LinearProgressIndicator()
          else if (_localPDFPath != null)
            Container(
              height: 200,
              child: PDFView(filePath: _localPDFPath!),
            )
          else
            ElevatedButton(
              onPressed: () => _downloadAndSavePDF(url),
              child: Text('Load PDF Preview'),
            ),
        ],
      );
    }
  }

  Widget _buildOptionLabel({
    required IconData icon,
    required String label,
    required bool isSelected,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: isSelected
              ? Theme.of(context).primaryColor
              : Colors.grey.shade600,
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildEventTypeDropdown() {
    return DropdownButtonFormField<Map<String, dynamic>>(
      value: selectedPlace,
      decoration: _buildInputDecoration(
          AppLocalizations.of(context)!.invitingPlace, Icons.event),
      items: invitedPlaces.map((place) {
        return DropdownMenuItem<Map<String, dynamic>>(
          value: place,
          child: Text(
            place['name'] ?? 'Unnamed Place',
            style: GoogleFonts.poppins(),
          ),
        );
      }).toList(),
      onChanged: (newValue) {
        setState(() {
          selectedPlace = newValue;
          _getPlacesName(newValue?['id']);
        });
      },
      validator: (value) => value == null
          ? AppLocalizations.of(context)!.validInvitingPlace
          : null,
    );
  }

  Widget _buildVenueDropdown() {
    return PlaceNames.isNotEmpty
        ? FadeInUp(
            duration: const Duration(milliseconds: 300),
            child: DropdownButtonFormField<Map<String, dynamic>>(
              value: selectedPlaceName!.isNotEmpty
                  ? selectedPlaceName
                  : PlaceNames[0],
              decoration: _buildInputDecoration(
                  AppLocalizations.of(context)!.placeName, Icons.event),
              items: PlaceNames.map((place) {
                return DropdownMenuItem<Map<String, dynamic>>(
                  value: place,
                  child: Text(
                    place['name'] ?? 'Unnamed Place',
                    style: GoogleFonts.poppins(),
                  ),
                );
              }).toList(),
              onChanged: (newValue) {
                setState(() {
                  selectedPlaceName = newValue;
                });
              },
              validator: (value) => value == null
                  ? AppLocalizations.of(context)!.validPlaceName
                  : null,
            ))
        : const LinearProgressIndicator();
  }

  Widget _buildHasCarCheckbox() {
    return FadeInUp(
        duration: const Duration(milliseconds: 300),
        child: CheckboxListTile(
          title: Text(AppLocalizations.of(context)!.hasCar),
          value: _hasCar,
          onChanged: (value) {
            setState(() {
              _hasCar = value ?? false;
            });
          },
        ));
  }

  Widget _buildCarPlateInput() {
    return FadeInUp(
        duration: const Duration(milliseconds: 300),
        child: TextFormField(
          controller: _carPlateController,
          decoration: _buildInputDecoration(
              AppLocalizations.of(context)!.carPlateNumber,
              Icons.directions_car),
          validator: (value) {
            if (_hasCar && (value == null || value.isEmpty)) {
              return AppLocalizations.of(context)!.validPlateNumber;
            }
            return null;
          },
        ));
  }

  Widget _buildHasLaptopCheckbox() {
    return FadeInUp(
        duration: const Duration(milliseconds: 300),
        child: CheckboxListTile(
          title: Text(AppLocalizations.of(context)!.haslaptop),
          value: _hasLaptop,
          onChanged: (value) {
            setState(() {
              _hasLaptop = value ?? false;
            });
          },
        ));
  }

  Widget _buildLaptopSerialInput() {
    return FadeInUp(
        duration: const Duration(milliseconds: 300),
        child: TextFormField(
          controller: _laptopSerialController,
          decoration: _buildInputDecoration(
              AppLocalizations.of(context)!.laptopSerial, Icons.laptop),
          validator: (value) {
            if (_hasLaptop && (value == null || value.isEmpty)) {
              return AppLocalizations.of(context)!.validSerialNumber;
            }
            return null;
          },
        ));
  }

  Widget _buildHasOtherItemsCheckbox() {
    return FadeInUp(
        duration: const Duration(milliseconds: 300),
        child: CheckboxListTile(
          title: Text(AppLocalizations.of(context)!.hasOtherItems),
          value: _hasOtherItems,
          onChanged: (value) {
            setState(() {
              _hasOtherItems = value ?? false;
            });
          },
        ));
  }

  Widget _buildOtherItemsInput() {
    return FadeInUp(
        duration: const Duration(milliseconds: 300),
        child: TextFormField(
          controller: _otherItemsController,
          decoration: _buildInputDecoration(
              AppLocalizations.of(context)!.otherItems, Icons.list),
          maxLines: 3, // Text area with 3 lines
          validator: (value) {
            if (_hasOtherItems && (value == null || value.isEmpty)) {
              return AppLocalizations.of(context)!.validOtherItems;
            }
            return null;
          },
        ));
  }

  Widget _buildDateTimeSelection() {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () => _showDatePicker(context),
            child: AbsorbPointer(
              child: TextFormField(
                decoration: _buildInputDecoration(
                  selectedDate != null
                      ? '${selectedDate!.day}-${selectedDate!.month}-${selectedDate!.year}'
                      : AppLocalizations.of(context)!.selectDate,
                  Icons.calendar_today,
                ),
                validator: (value) => selectedDate == null
                    ? AppLocalizations.of(context)!.pleaseSelectDate
                    : null,
              ),
            ),
          ),
        ),
       const SizedBox(width: 16),
        Expanded(
          child: GestureDetector(
            onTap: () => _showTimePicker(context),
            child: AbsorbPointer(
              child: TextFormField(
                decoration: _buildInputDecoration(
                  selectedTime != null
                      ? selectedTime!.format(context)
                      : AppLocalizations.of(context)!.selectTime,
                  Icons.access_time,
                ),
                validator: (value) => selectedTime == null
                    ? AppLocalizations.of(context)!.pleaseSelectTime
                    : null,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleSubmit,
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(0xFF2B2D3E),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
          shadowColor: Colors.black.withOpacity(0.3),
        ),
        child: _isLoading
            ? const CircularProgressIndicator(
                color: Color.fromARGB(255, 254, 254, 254))
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.save_outlined,
                      size: 24, color: Color.fromARGB(255, 252, 252, 252)),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context)!.saveGuest,
                    style: GoogleFonts.poppins(
                      color: const Color.fromARGB(255, 243, 243, 243),
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.of(context)!.phoneNumberRequired;
    }

    // Regular expression for Ethiopian phone numbers (09/07 followed by 8 digits)
    final RegExp phoneRegex = RegExp(r'^(09|07)[0-9]{8}$');

    if (!phoneRegex.hasMatch(value)) {
      return AppLocalizations.of(context)!.validPhoneFormat;
    }

    return null;
  }

  void _handleSubmit() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() => _isLoading = true);
      String formattedDate = DateFormat('dd/MM/yyyy').format(selectedDate!);
      String formattedTime =
          '${selectedTime?.hour.toString().padLeft(2, '0')}:${selectedTime?.minute.toString().padLeft(2, '0')}';

      try {
        String? token = await TokenManager.getToken();
        final guestService = GuestService();

        // Base data that's common for both individual and group
        final Map<String, dynamic> baseData = {
          'fullName': nameController.text.trim(),
          'phoneNumber': phoneController.text.trim(),
          'placeCategory': selectedPlace?['id'],
          'place': (selectedPlace?['id'] != 6)
              ? (selectedPlaceName?['id']) ?? (PlaceNames[0]['id'])
              : 0,
          'invitedDate': formattedDate,
          'invitedTime': formattedTime,
          'isMoreThanOneDay': _isMoreThanOneDay ? 1 : 0,
          'numberOfDays': _numberOfDaysController.text.trim(),
        };

        // Additional data for individual guests
        if (widget.category == 'Individual') {
          baseData.addAll({
            'haveCar': _hasCar ? 1 : 0,
            'car_plate': _carPlateController.text.trim(),
            'haveLaptop': _hasLaptop ? 1 : 0,
            'serial_number': _laptopSerialController.text.trim(),
            'haveItem': _hasOtherItems ? 1 : 0,
            'others': _otherItemsController.text.trim(),
          });
        }
        // Additional data for group guests
        else if (widget.category == 'Group') {
          // Check if file exists before trying to attach it
          if (_attachmentFile != null) {
            // Get file extension and MIME type
            final String fileExtension =
                _attachmentFile!.path.split('.').last.toLowerCase();
            final String mimeType = _getMimeType(fileExtension);

            // Create MultipartFile
            final file = await MultipartFile.fromFile(
              _attachmentFile!.path,
              filename: 'attachment.$fileExtension',
              contentType: MediaType.parse(mimeType),
            );
            baseData['file'] = file;
          }
        }

        // //Make API call
        final response = await guestService.updateGuest(
          token,
          baseData,
          widget.category == 'Individual' ? 1 : 2,
          guestData['id'],
        );

        if (response['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)!.savedsuccessfully),
              backgroundColor: Colors.green,
            ),
          );

          widget.onGuestAdded();
          _fetchGuest();
          _clearForm();
          Navigator.pop(context);
        } else {
          throw Exception(response['message'] ?? 'Failed to save guest');
        }
      } catch (e) {
       
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e.toString()),
            backgroundColor: Colors.red,
          ),
        );
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  // Helper method to determine MIME type
  String _getMimeType(String extension) {
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      default:
        return 'application/octet-stream';
    }
  }

  // Update _clearForm to also clear attachment
  void _clearForm() {
    nameController.clear();
    phoneController.clear();
    setState(() {
      selectedPlace = null;
      selectedPlaceName = null;
      _hasCar = false;
      _carPlateController.clear();
      _hasLaptop = false;
      _laptopSerialController.clear();
      _hasOtherItems = false;
      _isMoreThanOneDay = false;
      _numberOfDaysController.clear();
      _otherItemsController.clear();
      selectedDate = null;
      selectedTime = null;
      _attachmentFile = null;
      _attachmentType = AttachmentType.none;
      _isCapture = false;
    });
  }

  Future<void> _showDatePicker(BuildContext context) async {
    final bloc =
        BlocProvider.of<AlertCalenderControllerBloc>(context, listen: false);
    List? selectedValue = await showDialog<List>(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16), // Border radius of 16
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: SingleChildScrollView(
            child: BlocProvider.value(
              value: bloc,
              child: const AlertDatePicker(
                displayGregorianCalender: false,
                userLanguage: "am",
                startYear: 1990,
                endYear: 2020,
                todaysDateBackgroundColor: Color.fromARGB(255, 105, 4, 123),
              ),
            ),
          ),
        ),
      ),
    );

    // Handle the selected value if needed
    if (selectedValue != null) {
      // Do something with the selected value
      _updateSelectedDate(selectedValue);
    }
  }

  void _updateSelectedDate(List selectedValue) {
    String dateString = selectedValue.join('-');
    List<String> dateParts = dateString.split('-');
    if (dateParts.length == 3) {
      setState(() {
        selectedDate = DateTime(
          int.parse(dateParts[2]),
          int.parse(dateParts[1]),
          int.parse(dateParts[0]),
        );
      });
    }
  }

  Future<void> _showTimePicker(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedTime ?? TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() => selectedTime = picked);
    }
  }
}

class PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Only allow digits
    final newString = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    // Limit to 10 digits
    if (newString.length > 10) {
      return oldValue;
    }

    // Only allow starting with 09 or 07
    if (newString.length >= 2) {
      if (!newString.startsWith('09') && !newString.startsWith('07')) {
        return oldValue;
      }
    }

    return TextEditingValue(
      text: newString,
      selection: TextSelection.collapsed(offset: newString.length),
    );
  }
}
