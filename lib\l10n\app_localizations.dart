import 'package:flutter/widgets.dart';
import 'app_localizations_en.dart';
import 'app_localizations_am.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  // Define a map to hold language-specific messages
  static final Map<String, Map<String, String>> _localizedMessages = {
    'en': AppLocalizationsEn.messages,
    'am': AppLocalizationsAm.messages,
  };

  String translate(String key) {
    return _localizedMessages[locale.languageCode]?[key] ?? key;
  }

  String get hello => translate('hello');
  String get welcome => translate('welcome');
  String get signIn => translate('signIn');
  String get signInbtn => translate('signInbtn');
  String get username => translate('username');
  String get password => translate('password');
  String get forgotPassword => translate('forgotPassword');
  String get validationPassword => translate('validationPassword');
  String get validationUsername => translate('validationUsername');
  String get resetPassword => translate('resetPassword');
  String get resetPasswordOTP => translate('resetPasswordOTP');
  String get enterNewPassword => translate('enterNewPassword');
  String get newPassword => translate('newPassword');
  String get pleaseEnterNewPassword => translate('pleaseEnterNewPassword');
  String get sendOTP => translate('sendOTP');
  String get verifyOTP => translate('verifyOTP');
  String get backtoLogin => translate('backtoLogin');
  String get usernameOTP => translate('usernameOTP');
  String get homeScreen => translate('homeScreen');
  String get overview => translate('overview');
  String get thismonthData => translate('thismonth data');
  String get approved => translate('approved');
  String get rejected => translate('rejected');
  String get entered => translate('entered');
  String get statistics => translate('statistics');
  String get monthlyStatus => translate('monthlyStatus');
  String get inviteGuests => translate('inviteGuests');
  String get searchGuests => translate('searchGuests');
  String get noGuestsFound => translate('noGuestsFound');
  String get location => translate('location');
  String get dateTime => translate('dateTime');
  String get individual => translate('individual');
  String get group => translate('group');
  String get addIndividual => translate('addIndividual');
  String get addGroup => translate('addGroup');
  String get cancel => translate('cancel');
  String get confirmCancel => translate('confirmCancel');
  String get cancelMessage => translate('cancelMessage');
  String get close => translate('close');
  String get confirmApprove => translate('confirmApprove');
  String get confirmReject => translate('confirmReject');
  String get approveMessage => translate('approveMessage');
  String get rejectMessage => translate('rejectMessage');
  String get attachments => translate('attachments');
  String get contactInformation => translate('contactInformation');
  String get additionalInformation => translate('additionalInformation');
  String get carPlate => translate('carPlate');
  String get otherItems => translate('otherItems');
  String get laptopSerial => translate('laptopSerial');
  String get selectPerson => translate('selectPerson');
  String get approve => translate('approve');
  String get reject => translate('reject');
  String get entered_tx => translate('entered_tx');
  String get approved_tx => translate('approved_tx');
  String get rejected_tx => translate('rejected_tx');
  String get draft => translate('draft');
  String get canceled => translate('canceled');
  String get onprocess => translate('onprocess');
  String get phone => translate('phone');
  String get status => translate('status');
  String get category => translate('category');
  String get time => translate('time');
  String get date => translate('date');
  String get selectDate => translate('selectDate');
  String get pleaseSelectDate => translate('pleaseSelectDate');
  String get selectTime => translate('selectTime');
  String get pleaseSelectTime => translate('pleaseSelectTime');
  String get saveGuest => translate('saveGuest');
  String get phoneNumberRequired => translate('phoneNumberRequired');
  String get validPhoneFormat => translate('validPhoneFormat');
  String get validOtherItems => translate('validOtherItems');
  String get hasOtherItems => translate('hasOtherItems');
  String get validSerialNumber => translate('validSerialNumber');
  String get serialNumber => translate('serialNumber');
  String get haslaptop => translate('haslaptop');
  String get carPlateNumber => translate('carPlateNumber');
  String get validPlateNumber => translate('validPlateNumber');
  String get hasCar => translate('hasCar');
  String get validPlaceName => translate('validPlaceName');
  String get placeName => translate('placeName');
  String get validInvitingPlace => translate('validInvitingPlace');
  String get invitingPlace => translate('invitingPlace');
  String get captureDocument => translate('captureDocument');
  String get chooseFile => translate('chooseFile');
  String get existingRemoved => translate('existingRemoved');
  String get preview => translate('preview');
  String get capture => translate('capture');
  String get uploadFile => translate('uploadFile');
  String get documentAttachment => translate('documentAttachment');
  String get guestName => translate('guestName');
  String get groupLeaderName => translate('groupLeaderName');
  String get vaildGuestName =>
      translate('vaildGuestName'); // Corrected typo: Vaild -> Valid
  String get format => translate('format');
  String get addNewGuest => translate('addNewGuest');

  String get home => translate('home');
  String get inviteGuest => translate('inviteGuest');
  String get requests => translate('requests');
  String get account => translate('account');
  String get about => translate('about');
  String get logout => translate('logout');
  String get updatedsuccessfully => translate('updatedsuccessfully');
  String get incorrectPassword => translate('incorrectPassword');
  String get failedUpdatePassword => translate('failedUpdatePassword');
  String get updateYourPassword => translate('updateYourPassword');
  String get currentPassword => translate('currentPassword');
  String get confirmPassword => translate('confirmPassword');
  String get updating => translate('updating');
  String get updatingPassword => translate('updatingPassword');
  String get validPassword =>
      translate('validPassword'); // Corrected typo: vaild -> valid
  String get doNotMatch => translate('doNotMatch');
  String get changePassword => translate('changePassword');
  String get ensurePassword => translate('ensurePassword');
  String get guestRequests => translate('guestRequests');
  String get sentBy => translate('sentBy');
  String get rejectionReason => translate('rejectionReason');
  String get validRejection => translate('validRejection');
  String get savedsuccessfully => translate('savedsuccessfully');
  String get vaildregistration => translate('vaildregistration');
  String get search => translate('search');
  String get selectPersonFirst => translate('selectPersonFirst');
  String get send => translate('send');
  String get numberOfDays => translate('numberOfDays');
  String get isMoreThanOneDays => translate('isMoreThanOneDays');
  String get validateNumberOfDays => translate('validateNumberOfDays');
  String get vipRequests => translate('vipRequests');
  String get success => translate('success');
  String get failed => translate('failed');
  String get sentAt => translate('sentAt');
  String get approve_l => translate('approve_l');
  String get reject_l => translate('reject_l');
  String get verify_device => translate('verify_device');
  String get enter_otp_for_verification => translate('enter_otp_for_verification');
  

}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => ['en', 'am'].contains(locale.languageCode);

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(covariant LocalizationsDelegate<AppLocalizations> old) =>
      false;
}
