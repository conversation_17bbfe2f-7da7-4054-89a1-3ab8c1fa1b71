import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:abushakir/abushakir.dart';
import 'package:guest_mobile_app/controller/alert_calender_bloc/alert_calender_controller_bloc.dart';
import 'dart:ui';

class AlertDatePicker extends StatefulWidget {
  final bool displayGregorianCalender;
  final String userLanguage;
  final Color todaysDateBackgroundColor;
  final int startYear;
  final int endYear;

  const AlertDatePicker({
    Key? key,
    required this.displayGregorianCalender,
    required this.userLanguage,
    required this.startYear,
    required this.endYear,
    required this.todaysDateBackgroundColor,
  }) : super(key: key);

  @override
  _AlertDatePickerState createState() => _AlertDatePickerState();
}

class _AlertDatePickerState extends State<AlertDatePicker> {
  int? _selectedYear;

  @override
  void initState() {
    super.initState();
    _selectedYear = EtDatetime.now().year;
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AlertCalenderControllerBloc,
        AlertCalenderControllerState>(
      listener: (context, state) {},
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.9),
                Colors.white.withOpacity(0.7),
              ],
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1.5,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(context, state),
                    _buildCalendarGrid(context, state),
                    _buildFooter(context, state),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(
      BuildContext context, AlertCalenderControllerState state) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF2B2D3E),
            Color(0xFF1F2634),
          ],
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildGlassIconButton(
                Icons.chevron_left,
                () => BlocProvider.of<AlertCalenderControllerBloc>(context)
                    .add(PrevMonthCalendar(state.currentMoment)),
              ),
              Text(
                '${state.currentMoment.monthName} ${state.currentMoment.year}',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              _buildGlassIconButton(
                Icons.chevron_right,
                () => BlocProvider.of<AlertCalenderControllerBloc>(context)
                    .add(NextMonthCalendar(state.currentMoment)),
              ),
            ],
          ),
          SizedBox(height: 8),
          _buildGlassDropdown(state),
        ],
      ),
    );
  }

  Widget _buildGlassIconButton(IconData icon, VoidCallback onPressed) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white.withOpacity(0.2),
      ),
      child: IconButton(
        icon: Icon(icon, color: Colors.white),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildGlassDropdown(AlertCalenderControllerState state) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white.withOpacity(0.2),
      ),
      child: DropdownButton<int>(
        value: _selectedYear,
        dropdownColor: Colors.blue.withOpacity(0.9),
        icon: Icon(Icons.arrow_drop_down, color: Colors.white),
        isExpanded: true,
        underline: Container(),
        style: TextStyle(color: Colors.white, fontSize: 16),
        onChanged: (int? newValue) {
          if (newValue != null) {
            setState(() => _selectedYear = newValue);
            BlocProvider.of<AlertCalenderControllerBloc>(context)
                .add(CalenderByYear(state.currentMoment, newValue));
          }
        },
        items: List.generate(
          widget.endYear - widget.startYear + 1,
          (index) => DropdownMenuItem<int>(
            value: widget.startYear + index,
            child: Text('${widget.startYear + index}'),
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarGrid(
      BuildContext context, AlertCalenderControllerState state) {
    final monthDaysList = state.currentMoment.monthDays().toList();
    if (monthDaysList.isEmpty || monthDaysList[0].length < 4)
      return Container();

    final paddingDays = (monthDaysList[0][3] as num).toInt();

    return Container(
      height: MediaQuery.of(context).size.height * 0.4,
      padding: EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
          childAspectRatio: 1,
        ),
        itemCount: monthDaysList.length + paddingDays,
        itemBuilder: (context, index) {
          if (index < paddingDays) return Container();

          final dayIndex = index - paddingDays;
          final day = (monthDaysList[dayIndex][2] as num).toInt();
          final month = (monthDaysList[dayIndex][1] as num).toInt();
          final year = (monthDaysList[dayIndex][0] as num).toInt();

          final isSelected = _isDateSelected(state, day, month, year);
          final isToday = day == EtDatetime.now().day &&
              month == EtDatetime.now().month &&
              year == EtDatetime.now().year;

          return _buildDateCell(
            context,
            state,
            day,
            month,
            year,
            isSelected,
            isToday,
          );
        },
      ),
    );
  }

  // Widget _buildDateCell(
  //   BuildContext context,
  //   AlertCalenderControllerState state,
  //   int day,
  //   int month,
  //   int year,
  //   bool isSelected,
  //   bool isToday,
  // ) {
  //   return GestureDetector(
  //     onTap: () {
  //       if (isSelected) {
  //         BlocProvider.of<AlertCalenderControllerBloc>(context).add(
  //           RemoveItemFromList(
  //             '$day-$month-$year',
  //             state.currentMoment,
  //             int.parse('$day$month$year'),
  //           ),
  //         );
  //       } else {
  //         BlocProvider.of<AlertCalenderControllerBloc>(context).add(
  //           AddSingleValues(
  //             '$day-$month-$year',
  //             state.currentMoment,
  //             int.parse('$day$month$year'),
  //           ),
  //         );
  //       }
  //     },
  //     child: Container(
  //       decoration: BoxDecoration(
  //         gradient: isSelected || isToday
  //             ? LinearGradient(
  //                 colors: isSelected
  //                     ? [
  //                         Colors.blue.withOpacity(0.7),
  //                         Colors.purple.withOpacity(0.7)
  //                       ]
  //                     : [
  //                         widget.todaysDateBackgroundColor.withOpacity(0.7),
  //                         widget.todaysDateBackgroundColor
  //                       ],
  //                 begin: Alignment.topLeft,
  //                 end: Alignment.bottomRight,
  //               )
  //             : null,
  //         color: isSelected || isToday ? null : Colors.white.withOpacity(0.1),
  //         borderRadius: BorderRadius.circular(12),
  //         boxShadow: [
  //           BoxShadow(
  //             color: Colors.black.withOpacity(0.1),
  //             blurRadius: 4,
  //             spreadRadius: 0,
  //             offset: Offset(0, 2),
  //           ),
  //         ],
  //       ),
  //       child: Center(
  //         child: Text(
  //           '$day',
  //           style: TextStyle(
  //             color: isSelected || isToday ? Colors.white : Colors.black87,
  //             fontSize: 16,
  //             fontWeight:
  //                 isSelected || isToday ? FontWeight.bold : FontWeight.normal,
  //           ),
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildDateCell(
    BuildContext context,
    AlertCalenderControllerState state,
    int day,
    int month,
    int year,
    bool isSelected,
    bool isToday,
  ) {
    final today = EtDatetime.now();
    final selectedDate = EtDatetime(year: year, month: month, day: day);

    return GestureDetector(
      onTap: () {
        // Check if the selected date is before today
        if (selectedDate.isBefore(today)) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("You cannot select a past date."),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        if (isSelected) {
          BlocProvider.of<AlertCalenderControllerBloc>(context).add(
            RemoveItemFromList(
              '$day-$month-$year',
              state.currentMoment,
              int.parse('$day$month$year'),
            ),
          );
        } else {
          BlocProvider.of<AlertCalenderControllerBloc>(context).add(
            AddSingleValues(
              '$day-$month-$year',
              state.currentMoment,
              int.parse('$day$month$year'),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: isSelected || isToday
              ? LinearGradient(
                  colors: isSelected
                      ? [
                          Colors.blue.withOpacity(0.7),
                          Colors.purple.withOpacity(0.7)
                        ]
                      : [
                          widget.todaysDateBackgroundColor.withOpacity(0.7),
                          widget.todaysDateBackgroundColor
                        ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected || isToday ? null : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              spreadRadius: 0,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            '$day',
            style: TextStyle(
              color: isSelected || isToday ? Colors.white : Colors.black87,
              fontSize: 16,
              fontWeight:
                  isSelected || isToday ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(
      BuildContext context, AlertCalenderControllerState state) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF2B2D3E),
            Color(0xFF1F2634),
          ],
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildGlassButton('Cancel', () => Navigator.pop(context)),
          _buildGlassButton('OK', () {
            List<String> selectedDates = _getSelectedDates(state);
            if (selectedDates.isEmpty) {
              final now = EtDatetime.now();
              selectedDates.add('${now.day}-${now.month}-${now.year}');
            }
            Navigator.pop(context, selectedDates);
          }),
        ],
      ),
    );
  }

  Widget _buildGlassButton(String text, VoidCallback onPressed) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white.withOpacity(0.3),
      ),
      child: TextButton(
        onPressed: onPressed,
        child: Text(
          text,
          style: TextStyle(color: Colors.white, fontSize: 16),
        ),
      ),
    );
  }

  bool _isDateSelected(
      AlertCalenderControllerState state, int day, int month, int year) {
    final dateString = '$day-$month-$year';
    if (state is AddFirstValueState && state.firstDate == dateString)
      return true;
    if (state is AddSecondValueState && state.secondDate == dateString)
      return true;
    if (state is SingleValuesIndexState && state.singleDatesList == dateString)
      return true;
    return false;
  }

  List<String> _getSelectedDates(AlertCalenderControllerState state) {
    final selectedDates = <String>[];
    if (state is AddFirstValueState) selectedDates.add(state.firstDate);
    if (state is AddSecondValueState) selectedDates.add(state.secondDate);
    if (state is SingleValuesIndexState)
      selectedDates.add(state.singleDatesList);
    return selectedDates;
  }
}
