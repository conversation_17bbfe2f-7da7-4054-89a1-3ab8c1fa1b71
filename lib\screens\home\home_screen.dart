import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:shimmer/shimmer.dart';
import 'package:guest_mobile_app/core/services/auth_service.dart';
import 'package:guest_mobile_app/core/services/dashboard_service.dart';
import 'package:guest_mobile_app/core/utils/tokenManager.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';
import 'package:guest_mobile_app/widgets/custom_drawer.dart';
import 'package:guest_mobile_app/providers/language_provider.dart';
import 'package:provider/provider.dart';

class HomeScreen extends StatefulWidget {
  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  // Add these variables
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  Map<String, dynamic>? _profileData;
  bool _isLoading = true;
  int? approved = 0;
  int? rejected = 0;
  int? entered = 0;
  final List<Map<String, dynamic>> data = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(0, 0.5, curve: Curves.easeInOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutBack,
      ),
    );

    _animationController.forward();
    _fetchProfile();
    _fetchCurrentMonthData();
    _fetchCurrentYearData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _fetchProfile() async {
    try {
      final authService = AuthService();
      final profileData = await authService.getProfile();

      setState(() {
        _profileData = profileData['data'];
        _isLoading = false;
      });
    } catch (e) {
      
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _fetchCurrentYearData() async {
    setState(() => _isLoading = true);

    try {
      String? token = await TokenManager.getToken();
      final dashBoardService = DashBoardService();
      final response = await dashBoardService.getCurrentYearData(token);

      // Validate and cast the data
      if (response['data'] != null && response['data'] is List) {
        final fetchedData = (response['data'] as List)
            .map((item) => item as Map<String, dynamic>)
            .toList();
        data.addAll(fetchedData);
      } else {
        throw Exception('Invalid data format');
      }
    } catch (e) {
      
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchCurrentMonthData() async {
    setState(() => _isLoading = true);

    try {
      String? token = await TokenManager.getToken();
      final dashBoardService = DashBoardService();
      final response = await dashBoardService.getCurrentMonthData(token);

      if (response['data'] != null && response['data'] is List) {
        response['data'].forEach((element) {
          if (element['statusLabel'] == 'approved') {
            approved = element['total'];
          } else if (element['statusLabel'] == 'rejected') {
            rejected = element['total'];
          } else if (element['statusLabel'] == 'entered') {
            entered = element['total'];
          }
        });
      } else {
        throw Exception('Invalid data format');
      }
    } catch (e) {
      
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _changeLanguage(Locale locale) {
    Provider.of<LanguageProvider>(context, listen: false).setLocale(locale);
  }

  Widget _buildDashboard() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, 50 * (1 - _fadeAnimation.value)),
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: Transform.scale(
                      scale: _scaleAnimation.value,
                      child: child,
                    ),
                  ),
                );
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 24),
                  _buildStatsRow(),
                ],
              ),
            ),
            const SizedBox(height: 24),
            _buildAnimatedGraph(),
          ],
        ),
      ),
    );
  }

  int safeTotal(dynamic total) {
    if (total == null || total is! int || total < 0) return 0;
    return total;
  }

  Map<int, Map<String, int>> groupDataByMonthAndStatus(
      List<Map<String, dynamic>> data) {
    final Map<int, Map<String, int>> grouped = {};

    for (var item in data) {
      int month = item['month'];
      String statusLabel = item['statusLabel'];
      int total = safeTotal(item['total']); // Ensure valid values

      grouped.putIfAbsent(month, () => {});
      grouped[month]![statusLabel] =
          (grouped[month]![statusLabel] ?? 0) + total;
    }

    return grouped;
  }

  Widget _buildLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _legendItem(AppLocalizations.of(context)!.approved, Colors.blue),
        const SizedBox(width: 16),
        _legendItem(AppLocalizations.of(context)!.rejected, Colors.red),
      ],
    );
  }

  Widget _legendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 4),
        Text(label),
      ],
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.overview,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w800,
                color: Colors.black87,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context)!.thismonthData,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
                letterSpacing: 0.5,
              ),
        ),
      ],
    );
  }

  Widget _buildStatsRow() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Wrap(
          spacing: 16,
          runSpacing: 16,
          children: [
            _buildAnimatedCard(
              title: AppLocalizations.of(context)!.approved,
              value: approved.toString(),
              icon: Icons.verified_user_rounded,
              color: Colors.green,
            ),
            _buildAnimatedCard(
              title: AppLocalizations.of(context)!.rejected,
              value: rejected.toString(),
              icon: Icons.cancel_rounded,
              color: Colors.red,
            ),
            _buildAnimatedCard(
              title: AppLocalizations.of(context)!.entered,
              value: entered.toString(),
              icon: Icons.login_rounded,
              color: Colors.blue,
            ),
          ],
        );
      },
    );
  }

  Widget _buildAnimatedCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOutBack,
      width: 160,
      height: 140,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [color.withOpacity(0.2), color.withOpacity(0.05)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, size: 28, color: color),
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Text(
                value,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.w800,
                      color: color,
                    ),
              ),
            ),
            const SizedBox(height: 4),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedGraph() {
    final groupedData = groupDataByMonthAndStatus(data);

    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0, end: 1),
      duration: const Duration(milliseconds: 1000),
      curve: Curves.easeInOutCubic,
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 50 * (1 - value)),
            child: child,
          ),
        );
      },
      child: _buildGraph(),
    );
  }

  // Update loading states with shimmer effect
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildModernAppBar(),
      drawer: _isLoading
          ? _buildShimmerDrawer()
          : CustomDrawer(profileData: _profileData ?? {}),
      body: _isLoading ? _buildShimmerLoading() : _buildDashboard(),
    );
  }

  AppBar _buildModernAppBar() {
    return AppBar(
      title: Text(
        AppLocalizations.of(context)!.homeScreen,
        style: TextStyle(
          fontWeight: FontWeight.w800,
          color: Colors.black87,
          fontSize: 24,
        ),
      ),
      centerTitle: false,
      elevation: 0,
      backgroundColor: Colors.transparent,
      iconTheme: IconThemeData(color: Colors.grey.shade800),
      actions: [
        _buildLanguageButton(),
      ],
    );
  }

  Widget _buildLanguageButton() {
    return Container(
      margin: EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        shape: BoxShape.circle,
      ),
      child: PopupMenuButton<Locale>(
        icon: Icon(Icons.translate_rounded, color: Colors.grey.shade700),
        onSelected: (locale) => _changeLanguage(locale),
        itemBuilder: (context) => [
          PopupMenuItem(
            value: Locale('en'),
            child: Text('English 🇺🇸'),
          ),
          PopupMenuItem(
            value: Locale('am'),
            child: Text('Amharic 🇪🇹'),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerLoading() {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Header shimmer
              Container(
                width: 200,
                height: 28,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const SizedBox(height: 16),
              // Stats shimmer (horizontal scroll)
              SizedBox(
                height: 140,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: List.generate(
                      3,
                      (index) => Container(
                        width: 160,
                        height: 120,
                        margin: const EdgeInsets.only(right: 16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 32),
              // Graph shimmer
              Container(
                height: 300,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerDrawer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Drawer(
        child: Container(color: Colors.white),
      ),
    );
  }

  // Update chart styling
  Widget _buildGraph() {
    final groupedData = groupDataByMonthAndStatus(data);

    return Container(
      padding: const EdgeInsets.all(16.0),
      height: 400,
      child: PhysicalModel(
        elevation: 8,
        color: Colors.transparent,
        shadowColor: Colors.grey.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Text(
                  AppLocalizations.of(context)!.monthlyStatus,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w800,
                      ),
                ),
                const SizedBox(height: 16),
                _buildLegend(),
                const SizedBox(height: 16),
                Expanded(
                  child: BarChart(
                    BarChartData(
                      // Keep existing chart configuration
                      // Update colors and styling:
                      barTouchData: BarTouchData(
                        touchTooltipData: BarTouchTooltipData(
                          getTooltipColor: (group) =>
                              Colors.blueGrey.withOpacity(0.9),
                          getTooltipItem: (group, groupIndex, rod, rodIndex) {
                            final status = rodIndex == 0
                                ? AppLocalizations.of(context)!.approved
                                : AppLocalizations.of(context)!.rejected;
                            return BarTooltipItem(
                              '$status: ${rod.toY.toInt()}',
                                TextStyle(
                                color:   Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            );
                          },
                        ),
                      ),
                      titlesData: FlTitlesData(
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true, // Ensure titles are shown
                            getTitlesWidget: (double value, TitleMeta meta) =>
                                Text(
                              monthName(value.toInt() +
                                  1), // Custom function to display titles
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true, // Ensure titles are shown
                            getTitlesWidget: (double value, TitleMeta meta) {
                              return Text(
                                'Title $value', // Custom logic for your title
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 10,
                                ),
                              );
                            },
                          ),
                        ),
                      ),

                      borderData: FlBorderData(
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.shade200),
                          left: BorderSide(color: Colors.grey.shade200),
                        ),
                      ),
                      gridData: FlGridData(
                        horizontalInterval: 1,
                        drawHorizontalLine: true,
                        getDrawingHorizontalLine: (value) => FlLine(
                          color: Colors.grey.shade100,
                          strokeWidth: 1,
                        ),
                      ),
                      barGroups: groupedData.entries.map((entry) {
                        final month = entry.key;
                        final statuses = entry.value;
                        return BarChartGroupData(
                          x: month - 1,
                          barsSpace: 4,
                          barRods: [
                            BarChartRodData(
                              toY: statuses['approved']?.toDouble() ?? 0,
                              color: Colors.blue.shade400,
                              borderRadius: BorderRadius.circular(4),
                              width: 20,
                              backDrawRodData: BackgroundBarChartRodData(
                                show: true,
                                color: Colors.blue.shade50,
                              ),
                            ),
                            BarChartRodData(
                              toY: statuses['rejected']?.toDouble() ?? 0,
                              color: Colors.red.shade400,
                              borderRadius: BorderRadius.circular(4),
                              width: 20,
                              backDrawRodData: BackgroundBarChartRodData(
                                show: true,
                                color: Colors.red.shade50,
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String monthName(int month) {
    const List<String> months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    if (month < 1 || month > 12) return ''; // Handle out-of-range values
    return months[month - 1];
  }
}
