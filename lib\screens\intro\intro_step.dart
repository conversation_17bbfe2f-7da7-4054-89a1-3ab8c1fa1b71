// import 'package:flutter/material.dart';

// class IntroStep extends StatelessWidget {
//   final String title;
//   final String description;
//   final String imagePath;

//   const IntroStep({
//     Key? key,
//     required this.title,
//     required this.description,
//     required this.imagePath,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Image.asset(
//           imagePath,
//           height: 300,
//           width: 300,
//           fit: BoxFit.contain,
//         ),
//         SizedBox(height: 30),
//         Text(
//           title,
//           style: TextStyle(
//             fontSize: 26,
//             fontWeight: FontWeight.bold,
//             color: Colors.black87,
//             letterSpacing: 1.2,
//           ),
//           textAlign: TextAlign.center,
//         ),
//         SizedBox(height: 15),
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 24.0),
//           child: Text(
//             description,
//             style: TextStyle(
//               fontSize: 16,
//               color: Colors.grey[600],
//               height: 1.5,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       ],
//     );
//   }
// }

