import 'package:dio/dio.dart';
import 'package:guest_mobile_app/core/utils/apiClient.dart';

class AccountService {
  final ApiClient apiClient = ApiClient();

  //change password
   Future<Map<String, dynamic>> changePassword(
      String? token, String oldPassword, String newPassword) async {
    try {
      final response = await apiClient.dio.post(
        '/user/change/password',
        data: {
          "currentPassword": oldPassword,
          "newPassword": newPassword,
        },
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': response.data ?? 'Password changed successfully',
        };
      } else {
        return {
          'success': false,
          'message': response.data ?? 'Unknown error occurred',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data ?? 'Network error occurred',
      };
    } catch (e) {
     
      return {
        'success': false,
        'message': 'An unexpected error occurred',
      };
    }
  }
}
