import 'package:dio/dio.dart';
import 'package:guest_mobile_app/core/utils/apiClient.dart';

class RequestsService {
  final ApiClient apiClient = ApiClient();

  Future<Map<String, dynamic>> getRequests(String? token, String query) async {
    try {
      final response = await apiClient.dio.post(
        '/requests',
        data: {
          "query": query,
          "skip": 0,
          "step": 20,
        },
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data['data']['data'] ?? [],
          'total': response.data['total'] ?? 0,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error occurred',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data?['message'] ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'An unexpected error occurred',
      };
    }
  }

  Future<Map<String, dynamic>> saveApproveChanges(String? token,
      String? invitedDate, String? invitedTime, int? id, int? type) async {
    try {
      final response = await apiClient.dio.put(
        type == 1
            ? '/mobile/requests/approve/$id'
            : '/mobile/requests/group/approve/$id',
        data: {'invitedDate': invitedDate, 'invitedTime': invitedTime},
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Check if the request was successful
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        return {
          'success': true,
          'message': responseData ?? 'successfully make changes!',
        };
      } else {
        return {
          'success': false,
          'message': response.data ?? 'Failed to make changes',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error: $e',
      };
    }
  }

  Future<Map<String, dynamic>> saveRejectChanges(
      String? token, String? reason, int? id, int? type) async {
    try {
      final response = await apiClient.dio.put(
        type == 1 ? '/requests/reject/$id' : '/requests/group/reject/$id',
        data: {'reason': reason},
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      // Check if the request was successful
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        return {
          'success': true,
          'message': responseData ?? 'successfully make changes!',
        };
      } else {
        return {
          'success': false,
          'message': response.data ?? 'Failed to make changes',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error: $e',
      };
    }
  }

  Future<Map<String, dynamic>> getAttachments(String? token, int id) async {
    try {
      final response = await apiClient.dio.get(
        '/guests/group/others/details/request/$id',
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data['data'] ?? [],
        };
      } else {
        return {
          'success': false,
          'message': response.data ?? 'Unknown error occurred',
        };
      }
    } on DioException catch (e) {
      return {
        'success': false,
        'message': e.response?.data ?? 'Network error occurred',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'An unexpected error occurred',
      };
    }
  }
}
