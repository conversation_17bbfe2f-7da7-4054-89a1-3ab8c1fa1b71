import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:guest_mobile_app/core/constants/constants.dart';
import 'package:guest_mobile_app/core/services/requests_service.dart';
import 'package:guest_mobile_app/core/utils/tokenManager.dart';
import 'package:guest_mobile_app/widgets/custom_dialog.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';
import 'package:path_provider/path_provider.dart'
    show getApplicationDocumentsDirectory;

class RequestDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> guest;
  final VoidCallback onRequestSent;
  RequestDetailsScreen(
      {Key? key, required this.guest, required this.onRequestSent})
      : super(key: key);
  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  _RequestDetailsScreenState createState() => _RequestDetailsScreenState();
}

class _RequestDetailsScreenState extends State<RequestDetailsScreen>
    with TickerProviderStateMixin {
  final double infoHeight = 240.0;
  late AnimationController animationController;
  late Animation<double> animation;
  double opacity1 = 0.0;
  double opacity2 = 0.0;
  double opacity3 = 0.0;
  String? selectedName;
  bool _isLoading = true;
  String? selectedPhone;
  int? selectedId;
  List<Map<String, dynamic>> peopleList = [];
  String fileUrl = '';

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
        duration: const Duration(milliseconds: 1000), vsync: this);
    animation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
        parent: animationController,
        curve: Interval(0, 1.0, curve: Curves.fastOutSlowIn)));
    if (widget.guest['category'] != 'Individual') {
      _fetchAttacments();
    }
    setData();
  }

  void _processGuestsData(List data) {
    const statusLabels = [
      '',
      'Draft',
      'በመጠበቅ ላይ',
      'Approved',
      'Rejected',
      'Canceled',
      'in the courtyard'
    ];
  }

  Future<void> _fetchAttacments() async {
    setState(() => _isLoading = true);

    try {
      String? token = await TokenManager.getToken();
      final requestsService = RequestsService();
      final response =
          await requestsService.getAttachments(token, widget.guest['guestId']);

      if (response['data'] != null) {
        setState(() => fileUrl =
            AppConstants.apiBaseIP + "/" + response['data']['filePath']);
      } else {
        throw Exception('Invalid data format');
      }
    } catch (e) {
      
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // Perform Approval Logic
  void _performApproval() async {
    setState(() => _isLoading = true);
    try {
      String? token = await TokenManager.getToken();
      final requestsService = RequestsService();
      String invitedTime = widget.guest['invitedTime'];
      int guest_type = widget.guest['category'] == 'Individual' ? 1 : 2;
      String invitedDate = widget.guest['invitedDate'];
      int? id = widget.guest['guestId'];

      final response = await requestsService.saveApproveChanges(
          token, invitedDate, invitedTime, id, guest_type);

      if (response['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.success),
            backgroundColor: Colors.green,
          ),
        );
        widget.onRequestSent();
        Navigator.pop(context);
      } else {
        throw Exception(response['message'] ?? 'Failed to save guest');
      }
    } catch (e) {
     
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.failed),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // Perform Rejection Logic
  void _performRejection(String reason) async {
    setState(() => _isLoading = true);
    try {
      String? token = await TokenManager.getToken();

      int guest_type = widget.guest['category'] == 'Individual' ? 1 : 2;
      final requestsService = RequestsService();
      int? id = widget.guest['guestId'];

      final response = await requestsService.saveRejectChanges(
          token, reason, id, guest_type);

      if (response['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.success),
            backgroundColor: Colors.green,
          ),
        );
        widget.onRequestSent();
        Navigator.pop(context);
      } else {
        throw Exception(response['message'] ?? 'Failed to made changes');
      }
    } catch (e) {
     
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.failed),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> setData() async {
    animationController.forward();
    await Future<dynamic>.delayed(const Duration(milliseconds: 200));
    setState(() {
      opacity1 = 1.0;
    });
    await Future<dynamic>.delayed(const Duration(milliseconds: 200));
    setState(() {
      opacity2 = 1.0;
    });
    await Future<dynamic>.delayed(const Duration(milliseconds: 200));
    setState(() {
      opacity3 = 1.0;
    });
  }

  Color getStatusColor(int status) {
    switch (status) {
      case 1:
        return Colors.blue;
      case 3:
        return Colors.green;
      case 2:
        return Colors.orange;
      case 4:
        return Colors.red;
      case 5:
        return Colors.teal;
      case 6:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildStatusBar() {
    final statusColor = getStatusColor(widget.guest['status_value']);
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: statusColor.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            widget.guest['status'],
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double tempHeight = MediaQuery.of(context).size.height -
        (MediaQuery.of(context).size.width / 1.8) +
        24.0;

    return Container(
      color: Colors.white,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        floatingActionButton: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height / 6,
              child: _buildBottomBar(context)),
        ),
        floatingActionButtonLocation:
            FloatingActionButtonLocation.miniCenterFloat,
        body: Stack(
          children: <Widget>[
            Column(
              children: <Widget>[
                // Add status bar at the top
                AspectRatio(
                  aspectRatio: 1.8,
                  child: Image.asset(
                    'assets/images/rb_2149322234.png',
                    fit: BoxFit.cover,
                  ),
                ),
              ],
            ),
            Positioned(
              top: (MediaQuery.of(context).size.width / 1.8) - 24.0,
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(32.0),
                    topRight: Radius.circular(32.0),
                  ),
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      offset: const Offset(1.1, 1.1),
                      blurRadius: 10.0,
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.only(left: 8, right: 8),
                  child: SingleChildScrollView(
                    child: Container(
                      constraints: BoxConstraints(
                        minHeight: infoHeight,
                        maxHeight:
                            tempHeight > infoHeight ? tempHeight : infoHeight,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(25.0),
                        child: _buildContent(context),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              top: (MediaQuery.of(context).size.width / 1.8) - 24.0 - 35,
              right: 35,
              child: ScaleTransition(
                alignment: Alignment.center,
                scale: CurvedAnimation(
                  parent: animationController,
                  curve: Curves.fastOutSlowIn,
                ),
                child: Card(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(50.0),
                  ),
                  elevation: 10.0,
                  child: Container(
                    width: 60,
                    height: 60,
                    child: Center(
                      child: IconButton(
                        icon: const Icon(Icons.person_pin_outlined,
                            color: Color.fromARGB(255, 168, 75, 244)),
                        onPressed: () => {},
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).padding.top,
              left: 10,
              child: CircleAvatar(
                backgroundColor: Color.fromARGB(255, 201, 144, 247),
                child: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 100), // Add bottom padding
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildStatusBar(),
            const SizedBox(height: 24),
            _buildFeatures(),
            const SizedBox(height: 24),
            _buildDetails(),
            const SizedBox(height: 24),
            if (widget.guest['category'] == 'Individual' &&
                    widget.guest['haveCar'] == 1 ||
                widget.guest['category'] == 'Individual' &&
                    widget.guest['haveItem'] == 1 ||
                widget.guest['category'] == 'Individual' &&
                    widget.guest['haveLaptop'] == 1 ||
                widget.guest['isMoreThanOneDay'] == 1) ...[
              _buildAdditionalInfo(),
              const SizedBox(height: 24),
            ],
            if (fileUrl.isNotEmpty) ...[
              _buildAttachmentPreview(),
              const SizedBox(height: 24),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentPreview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.attachments,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              _buildFilePreview(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFilePreview() {
    final extension = fileUrl.split('.').last.toLowerCase();
    final isPDF = extension == 'pdf';
    final isImage = ['jpg', 'jpeg', 'png', 'gif'].contains(extension);

    if (isPDF) {
      return _buildPDFPreview();
    } else if (isImage) {
      return _buildImagePreview();
    } else {
      return _buildUnsupportedFileType();
    }
  }

  Widget _buildPDFPreview() {
    return Container(
      height: 200,
      width: double.infinity,
      child: FutureBuilder<String>(
        future: _downloadAndSavePDF(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(
              child: Text('Error loading PDF: ${snapshot.error}'),
            );
          }
          if (snapshot.hasData) {
            return Stack(
              children: [
                PDFView(
                  filePath: snapshot.data!,
                  enableSwipe: true,
                  swipeHorizontal: false,
                  autoSpacing: true,
                  pageSnap: true,
                  fitPolicy: FitPolicy.BOTH,
                ),
                Positioned(
                  right: 8,
                  top: 8,
                  child: IconButton(
                    icon: Icon(Icons.fullscreen, color: Colors.blue.shade400),
                    onPressed: () => _showFullScreenPDF(snapshot.data!),
                  ),
                ),
              ],
            );
          }
          return Center(child: Text('No PDF preview available'));
        },
      ),
    );
  }

  Widget _buildImagePreview() {
    return Container(
      height: 200,
      width: double.infinity,
      child: Stack(
        children: [
          Image.network(
            fileUrl,
            fit: BoxFit.contain,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return Center(
                child: Text('Error loading image: $error'),
              );
            },
          ),
          Positioned(
            right: 8,
            top: 8,
            child: IconButton(
              icon: Icon(Icons.fullscreen,
                  color: Color.fromARGB(255, 190, 52, 240)),
              onPressed: () => _showFullScreenImage(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnsupportedFileType() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(Icons.file_present, color: Colors.grey),
          SizedBox(width: 8),
          Text(
            'Unsupported file type',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Future<String> _downloadAndSavePDF() async {
    final dio = Dio();
    final dir = await getApplicationDocumentsDirectory();
    final fileName = fileUrl.split('/').last;
    final filePath = '${dir.path}/$fileName';

    // Check if file already exists
    final file = File(filePath);
    if (await file.exists()) {
      return filePath;
    }

    try {
      await dio.download(
        fileUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = (received / total * 100).toStringAsFixed(0);
           
            // You can implement a progress indicator here if needed
          }
        },
      );
      return filePath;
    } catch (e) {
     
      throw Exception('Failed to download file: $e');
    }
  }

  void _showFullScreenPDF(String filePath) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text('PDF Viewer'),
            backgroundColor: Colors.blue.shade400,
          ),
          body: PDFView(
            filePath: filePath,
            enableSwipe: true,
            swipeHorizontal: false,
            autoSpacing: true,
            pageSnap: true,
            fitPolicy: FitPolicy.BOTH,
          ),
        ),
      ),
    );
  }

  void _showFullScreenImage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('Image Viewer',
                style: TextStyle(color: Color.fromARGB(255, 255, 255, 255))),
            backgroundColor: const Color.fromARGB(255, 158, 66, 245),
          ),
          body: Center(
            child: InteractiveViewer(
              panEnabled: true,
              boundaryMargin: EdgeInsets.all(20),
              minScale: 0.5,
              maxScale: 4,
              child: Image.network(
                fileUrl,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Guest Name
              Text(
                widget.guest['name'],
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.location_on_outlined,
                      size: 18, color: Color.fromARGB(255, 168, 75, 244)),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '${widget.guest['invitedPlace']} - ${widget.guest['placeName']}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFeatures() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildFeatureItem(Icons.category_outlined,
              AppLocalizations.of(context)!.category, widget.guest['category']),
          _buildFeatureItem(Icons.access_time_outlined,
              AppLocalizations.of(context)!.time, widget.guest['invitedTime']),
          _buildFeatureItem(Icons.calendar_today_outlined,
              AppLocalizations.of(context)!.date, widget.guest['invitedDate']),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String value) {
    return Column(
      children: [
        Icon(icon, color: Color.fromARGB(255, 168, 75, 244)),
        const SizedBox(height: 8),
        Text(
          title,
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.contactInformation,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              _buildDetailRow(Icons.phone_outlined,
                  AppLocalizations.of(context)!.phone, widget.guest['phone']),
              const Divider(height: 20),
              _buildDetailRow(Icons.verified_outlined,
                  AppLocalizations.of(context)!.status, widget.guest['status']),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.additionalInformation,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              if (widget.guest['isMoreThanOneDay'] == 1)
                _buildDetailRow(
                    Icons.calendar_month,
                    AppLocalizations.of(context)!.numberOfDays,
                    widget.guest['numberOfDays'].toString()),
              if (widget.guest['haveCar'] == 1)
                _buildDetailRow(
                    Icons.directions_car_outlined,
                    AppLocalizations.of(context)!.carPlateNumber,
                    widget.guest['car_plate']),
              if (widget.guest['haveCar'] == 1) const Divider(height: 20),
              if (widget.guest['haveItem'] == 1)
                _buildDetailRow(
                    Icons.inventory_2_outlined,
                    AppLocalizations.of(context)!.otherItems,
                    widget.guest['others']),
              if (widget.guest['haveItem'] == 1) const Divider(height: 20),
              if (widget.guest['haveLaptop'] == 1)
                _buildDetailRow(
                    Icons.laptop_mac_outlined,
                    AppLocalizations.of(context)!.laptopSerial,
                    widget.guest['serial_number']),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Color.fromARGB(255, 168, 75, 244)),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget buildModernActionButtons({
    required VoidCallback onApprove,
    required VoidCallback onReject,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Reject Button
        widget.guest['status_value'] == 2 || widget.guest['status_value'] == 3
            ? GestureDetector(
                onTap: onReject,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.red.shade400, Colors.red.shade600],
                    ),
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.shade200,
                        blurRadius: 6,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.close, color: Colors.white),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.reject,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : Container(),

        // Approve Button
        widget.guest['status_value'] == 2 || widget.guest['status_value'] == 4
            ? GestureDetector(
                onTap: onApprove,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.green.shade400, Colors.green.shade600],
                    ),
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.shade200,
                        blurRadius: 6,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.check, color: Colors.white),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.approve,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : Container(),
      ],
    );
  }

  Widget _buildBottomBar(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: const EdgeInsets.only(bottom: 8), // Reduced bottom margin
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Color.fromARGB(154, 255, 255, 255),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Color.fromARGB(160, 245, 245, 245),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: buildModernActionButtons(
        onApprove: () {
          showApproveConfirmation(context);
        },
        onReject: () {
          showRejectReasonPopup(context);
        },
      ),
    );
  }

  void showApproveConfirmation(BuildContext context) {
    Navigator.of(context).push(CustomDialogRoute(
      builder: (context) => Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.check_circle_outline,
                    size: 48,
                    color: Colors.green,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.confirmApprove,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    AppLocalizations.of(context)!.approveMessage,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.cancel,
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _performApproval();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.approve,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }

// Modern reject reason dialog
  void showRejectReasonPopup(BuildContext context) {
    final TextEditingController reasonController = TextEditingController();

    Navigator.of(context).push(CustomDialogRoute(
      builder: (context) => Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.close_sharp,
                    size: 48,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.rejectionReason,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    AppLocalizations.of(context)!.validRejection,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: reasonController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Colors.grey.shade300,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Colors.grey.shade300,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Colors.red,
                          width: 2,
                        ),
                      ),
                      hintText: AppLocalizations.of(context)!.rejectionReason,
                      filled: true,
                      fillColor: Colors.grey.shade50,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.cancel,
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _performRejection(reasonController.text);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.reject,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }
}
