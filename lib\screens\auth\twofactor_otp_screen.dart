import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For FilteringTextInputFormatter
import 'package:geolocator/geolocator.dart';
import 'package:guest_mobile_app/core/services/auth_service.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';
import 'package:http/http.dart' as http;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class TwofactorOtpScreen extends StatefulWidget {
  @override
  _TwofactorOtpScreenState createState() => _TwofactorOtpScreenState();
}

class _TwofactorOtpScreenState extends State<TwofactorOtpScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;

  final _formKey = GlobalKey<FormState>();

  final List<TextEditingController> _otpControllers =
      List.generate(6, (index) => TextEditingController());

  bool _otpVerified = false;
  bool _isLoading = false;
  String? _token; // Token will now be used

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _fadeAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    );
    _controller.forward();
  }

  Future<Map<String, dynamic>> getDeviceDetails() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    String deviceName = '';
    String deviceType = '';

    if (kIsWeb) {
      WebBrowserInfo webInfo = await deviceInfo.webBrowserInfo;
      deviceName = webInfo.userAgent ?? 'Web';
      deviceType = 'Web';
    } else {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceName = androidInfo.model;
        deviceType = 'Android';
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceName = iosInfo.name;
        deviceType = 'iOS';
      }
    }

    // Get IP Address
    String ip = 'Unknown';
    try {
      final response =
          await http.get(Uri.parse('https://api64.ipify.org?format=json'));
      ip = jsonDecode(response.body)['ip'] ?? 'Unknown';
    } catch (_) {
      // IP fetch error
    }

    // Get Current Location
    Position? position;
    try {
      position = await Geolocator.getCurrentPosition();
    } catch (_) {
      // Location permission error or denied
    }

    // Reverse Geocode
    String country = 'Unknown';
    String state = 'Unknown';
    if (position != null) {
      try {
        final geoResponse = await http.get(Uri.parse(
            'https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.latitude}&lon=${position.longitude}&addressdetails=1'));
        final geoData = jsonDecode(geoResponse.body);
        country = geoData['address']?['country'] ?? 'Unknown';
        state = geoData['address']?['state'] ?? 'Unknown';
      } catch (_) {
        // Geocode error
      }
    }

    return {
      "device_type": deviceType,
      "device_name": deviceName,
      "ip_address": ip,
      "latitude": position?.latitude,
      "longitude": position?.longitude,
      "country": country,
      "state": state,
    };
  }

  void _verifyOtp() async {
    PermissionStatus? status;

    if (!kIsWeb) {
      status = await Permission.location.request();
    }

    // Check if permission is granted or if we're on web
    if (kIsWeb || (status != null && status.isGranted)) {
      String otp = _otpControllers.map((controller) => controller.text).join();

      if (otp.length == 6) {
        setState(() {
          _isLoading = true;
        });

        try {
          final authService = AuthService();
          final details = await getDeviceDetails();

          final response = await authService.verifyTwoFactorOtp(otp, details);
          setState(() {
            _isLoading = false;
          });
          if (response['success']) {
            setState(() {
              _otpVerified = true;
            });
            Navigator.pushReplacementNamed(context, '/home');
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Invalid OTP. Please try again.')),
            );
          }
        } catch (e) {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                e.toString().replaceFirst('Exception: ', ''),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else if (status != null && status.isDenied) {
        // Permission denied
      } else if (status != null && status.isPermanentlyDenied) {
        // Permission is permanently denied, guide the user to settings

        openAppSettings();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();

    for (var controller in _otpControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF2B2D3E),
              Color(0xFF1F2634),
            ],
          ),
        ),
        child: Center(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.lock_reset,
                        size: 80,
                        color: Colors.white,
                      ),
                      SizedBox(height: 20),
                      Text(
                        AppLocalizations.of(context)!.verify_device,
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        AppLocalizations.of(context)!
                            .enter_otp_for_verification,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                        ),
                      ),
                      SizedBox(height: 40),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: List.generate(
                          6,
                          (index) => SizedBox(
                            width: 45,
                            child: TextFormField(
                              controller: _otpControllers[index],
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 18),
                              maxLength: 1,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ], // Restrict to digits
                              decoration: InputDecoration(
                                counterText: '',
                                enabledBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                      color: Color(0xFF3F4251)),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                      color: Color(0xFF4A4D63), width: 2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                filled: true,
                                fillColor: Color(0xFF2B2D3E).withOpacity(0.3),
                              ),
                              onChanged: (value) {
                                if (value.isNotEmpty && index < 5) {
                                  FocusScope.of(context).nextFocus();
                                } else if (value.isEmpty && index > 0) {
                                  FocusScope.of(context).previousFocus();
                                }
                              },
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 30),
                      _isLoading
                          ? CircularProgressIndicator(color: Colors.white)
                          : SizedBox(
                              height: 56, // Fixed height
                              child: ElevatedButton(
                                onPressed: _isLoading ? null : (_verifyOtp),
                                style: ElevatedButton.styleFrom(
                                  padding: EdgeInsets.zero,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  backgroundColor: Colors.transparent,
                                  shadowColor: Colors.transparent,
                                ).copyWith(
                                  backgroundColor: MaterialStateProperty.all(
                                    Colors.transparent,
                                  ),
                                  overlayColor: MaterialStateProperty.all(
                                    Colors.white.withOpacity(0.1),
                                  ),
                                ),
                                child: Container(
                                  width: double.infinity,
                                  height: 56, // Match parent height
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Color(0xFF3F4251),
                                        Color(0xFF2B2D3E),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        blurRadius: 10,
                                        offset: Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: Center(
                                    child: _isLoading
                                        ? SizedBox(
                                            height: 24,
                                            width: 24,
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                              strokeWidth: 2,
                                            ),
                                          )
                                        : Text(
                                            AppLocalizations.of(context)!
                                                .verifyOTP,
                                            style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                  ),
                                ),
                              ),
                            ),
                      SizedBox(height: 20),
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.white.withOpacity(0.8),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.backtoLogin,
                          style: TextStyle(
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
