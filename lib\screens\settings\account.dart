import 'package:flutter/material.dart';
import 'package:guest_mobile_app/core/services/account_service.dart';
import 'package:guest_mobile_app/core/services/auth_service.dart';
import 'package:guest_mobile_app/core/utils/tokenManager.dart';
import 'package:guest_mobile_app/widgets/custom_drawer.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isCurrentPasswordVisible = false;
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;
  Map<String, dynamic>? _profileData;
  bool _isInitialLoading = true;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  @override
  void initState() {
    super.initState();

    _fetchProfile();
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _fetchProfile() async {
    try {
      final authService = AuthService();
      final profileData = await authService.getProfile();

      setState(() {
        _profileData = profileData['data'];
      });

      setState(() => _isInitialLoading = false);
    } catch (e) {
     
      setState(() => _isInitialLoading = false);
    }
  }

  bool _isPasswordValid(String password) {
    final passwordRegex = RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#/])[A-Za-z\d@$!%*?&#/]{8,}$',
    );
    return passwordRegex.hasMatch(password);
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      leading: IconButton(
        icon: const Icon(Icons.menu, color: Color(0xFF4A4A4A)),
        onPressed: () {
          _scaffoldKey.currentState?.openDrawer();
        },
      ),
      title: Text(
        AppLocalizations.of(context)!.changePassword,
        style: TextStyle(
          fontSize: 20,
          color: Color(0xFF4A4A4A),
          fontWeight: FontWeight.w500,
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.more_horiz, color: Color(0xFF4A4A4A)),
          onPressed: () {},
        ),
      ],
    );
  }

  Future<void> _onChangePassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      if (!mounted) return;
      String? token = await TokenManager.getToken();
      final accountService = AccountService();
      final response = await accountService.changePassword(
        token,
        _currentPasswordController.text,
        _newPasswordController.text,
      );

      if (response['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle,
                    color: Theme.of(context).colorScheme.onInverseSurface),
                const SizedBox(width: 8),
                Text(AppLocalizations.of(context)!.updatedsuccessfully),
              ],
            ),
            behavior: SnackBarBehavior.floating,
          ),
        );
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmPasswordController.clear();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error,
                    color: Theme.of(context).colorScheme.onInverseSurface),
                const SizedBox(width: 8),
                Text(AppLocalizations.of(context)!.incorrectPassword),
              ],
            ),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
     
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.failedUpdatePassword),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Widget _buildPasswordStrengthIndicator(String password) {
    int strength = 0;
    String text = 'Weak';
    Color color = Colors.red;

    if (password.length >= 8) strength++;
    if (password.contains(RegExp(r'[A-Z]'))) strength++;
    if (password.contains(RegExp(r'[a-z]'))) strength++;
    if (password.contains(RegExp(r'[0-9]'))) strength++;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) strength++;

    if (strength >= 5) {
      text = 'Strong';
      color = Colors.green;
    } else if (strength >= 3) {
      text = 'Medium';
      color = Colors.orange;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: strength / 5,
                  backgroundColor: Colors.grey.withOpacity(0.2),
                  color: color,
                  minHeight: 6,
                ),
              ),
            ),
            SizedBox(width: 12),
            Text(
              text,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildRequirementChip('8+ characters', password.length >= 8),
            _buildRequirementChip(
                'Uppercase', password.contains(RegExp(r'[A-Z]'))),
            _buildRequirementChip(
                'Lowercase', password.contains(RegExp(r'[a-z]'))),
            _buildRequirementChip(
                'Number', password.contains(RegExp(r'[0-9]'))),
            _buildRequirementChip('Special char',
                password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))),
          ],
        ),
      ],
    );
  }

  Widget _buildRequirementChip(String label, bool isMet) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isMet
            ? Colors.green.withOpacity(0.1)
            : Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isMet ? Colors.green : Colors.grey.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isMet ? Icons.check_circle : Icons.circle_outlined,
            size: 12,
            color: isMet ? Colors.green : Colors.grey,
          ),
          SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: isMet ? Colors.green : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required bool isVisible,
    required VoidCallback onToggleVisibility,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[700],
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        SizedBox(height: 8),
        TextFormField(
          controller: controller,
          obscureText: !isVisible,
          enableSuggestions: false,
          autocorrect: false,
          style: TextStyle(fontSize: 16),
          onChanged: (value) {
            if (label == AppLocalizations.of(context)!.newPassword) {
              setState(() {}); // Rebuild for password strength indicator
            }
          },
          decoration: InputDecoration(
            hintText: '••••••••',
            filled: true,
            fillColor: Colors.grey[100],
            contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide:
                  BorderSide(color: Theme.of(context).primaryColor, width: 2),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                isVisible ? Icons.visibility_off : Icons.visibility,
                color: Colors.grey[600],
              ),
              onPressed: onToggleVisibility,
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '$label is required';
            }
            if (label == AppLocalizations.of(context)!.newPassword &&
                !_isPasswordValid(value)) {
              return 'Password does not meet requirements';
            }
            if (label == AppLocalizations.of(context)!.confirmPassword &&
                value != _newPasswordController.text) {
              return AppLocalizations.of(context)!.doNotMatch;
            }
            return null;
          },
        ),
        if (label == AppLocalizations.of(context)!.newPassword) ...[
          SizedBox(height: 16),
          _buildPasswordStrengthIndicator(_newPasswordController.text),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      key: _scaffoldKey,
      appBar: _buildAppBar(),
      drawer: _isInitialLoading
          ? Drawer(child: const Center(child: CircularProgressIndicator()))
          : CustomDrawer(profileData: _profileData ?? {}),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue),
                        SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            AppLocalizations.of(context)!.ensurePassword,
                            style: TextStyle(
                              color: Colors.blue[800],
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 32),
                  _buildPasswordField(
                    controller: _currentPasswordController,
                    label: AppLocalizations.of(context)!.currentPassword,
                    isVisible: _isCurrentPasswordVisible,
                    onToggleVisibility: () => setState(() =>
                        _isCurrentPasswordVisible = !_isCurrentPasswordVisible),
                  ),
                  SizedBox(height: 24),
                  _buildPasswordField(
                    controller: _newPasswordController,
                    label: AppLocalizations.of(context)!.newPassword,
                    isVisible: _isNewPasswordVisible,
                    onToggleVisibility: () => setState(
                        () => _isNewPasswordVisible = !_isNewPasswordVisible),
                  ),
                  SizedBox(height: 24),
                  _buildPasswordField(
                    controller: _confirmPasswordController,
                    label: AppLocalizations.of(context)!.confirmPassword,
                    isVisible: _isConfirmPasswordVisible,
                    onToggleVisibility: () => setState(() =>
                        _isConfirmPasswordVisible = !_isConfirmPasswordVisible),
                  ),
                  SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _onChangePassword,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0xFF3F4251),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: _isLoading
                          ? SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              AppLocalizations.of(context)!.changePassword,
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
