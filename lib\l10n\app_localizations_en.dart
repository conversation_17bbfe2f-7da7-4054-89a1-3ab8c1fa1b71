class AppLocalizationsEn {
  static Map<String, String> get messages => {
        'signIn': 'Sign in to continue',
        'welcome': 'Welcome',
        'signInbtn': 'Sign In',
        'username': 'Username',
        'password': 'Password',
        'forgotPassword': 'Forgot Password?',
        'validationPassword': 'Please enter your password',
        'validationUsername': 'Please enter your username',
        'resetPassword': 'Reset Password',
        'resetPasswordOTP': 'Enter your OTP to reset password',
        'sentOTP': 'Enter the OTP sent to your phone',
        'enterNewPassword': 'Enter your new password',
        'newPassword': 'New Password',
        'pleaseEnterNewPassword': 'Please enter a new password',
        'sendOTP': 'Send OTP',
        'verifyOTP': 'Verify OTP',
        'backtoLogin': 'Back to Login',
        'usernameOTP': 'Enter your username to receive OTP',
        'homeScreen': 'Home Screen',
        'overview': 'Overview',
        'thismonth data': 'This Month Data',
        'approved': 'Approved',
        'rejected': 'Rejected',
        'approve': 'Approve',
        'reject': 'Reject',
        'entered': 'Entered',
        'entered_tx': 'Entered',
        'approved_tx': 'Approved',
        'rejected_tx': 'Rejected',
        'draft': 'Draft',
        'canceled': 'Canceled',
        'onprocess': 'On process',
        'statistics': 'Statistics',
        'monthlyStatus': 'Monthly Status Distribution',
        'inviteGuests': 'Invite Guests',
        'searchGuests': 'Search guests...',
        'noGuestsFound': 'No guests found',
        'location': 'Location',
        'dateTime': 'Date & Time',
        'individual': 'Individual',
        'group': 'Group',
        'addIndividual': 'Add Individual',
        'addGroup': 'Add Group',
        'cancel': 'Cancel',
        'confirmCancel': 'Confirm Cancel',
        'cancelMessage': 'Are you sure you want to cancel?',
        'close': 'Close',
        'confirmApprove': 'Confirm Approve',
        'confirmReject': 'Confirm Reject',
        'approveMessage': 'Are you sure you want to approve?',
        'rejectMessage': 'Are you sure you want to reject?',
        'attachments': 'Attachments',
        'contactInformation': 'Contact Information',
        'additionalInformation': 'Additional Information',
        'carPlate': 'Car Plate',
        'otherItems': 'Other Items',
        'laptopSerial': 'Laptop Serial',
        'selectPerson': 'Select Person',
        'phone': "Phone Number",
        'status': 'Status',
        'category': 'Category',
        'time': 'Time',
        'date': 'Date',
        'selectDate': 'Select Date',
        'pleaseSelectDate': 'Please select date',
        'selectTime': 'Select Time',
        'pleaseSelectTime': 'Please select time',
        'saveGuest': 'Save Guest',
        'phoneNumberRequired': 'Phone number is required',
        'validPhoneFormat': 'Enter valid format: ********** or **********',
        'validOtherItems': 'Please enter other items',
        'hasOtherItems': 'Has Other Items?',
        'validSerialNumber': 'Please enter laptop serial number',
        'serialNumber': 'Laptop Serial Number',
        'haslaptop': 'Has laptop?',
        'carPlateNumber': 'Car Plate Number',
        'validPlateNumber': 'Please enter car plate number',
        'hasCar': 'Has car?',
        'validPlaceName': 'Please select place name',
        'placeName': 'Place Name',
        'validInvitingPlace': 'Please select inviting place',
        'invitingPlace': 'Inviting place',
        'captureDocument': 'Capture Document',
        'chooseFile': 'Choose File',
        'existingRemoved': 'Existing image will be removed',
        'preview': 'Preview',
        'capture': 'Capture',
        'uploadFile': 'Upload File',
        'documentAttachment': 'Document Attachment',
        'guestName': 'Guest Name',
        'groupLeaderName': 'Group leader name',
        'vaildGuestName': 'Please enter guest name',
        'format': 'Format: ********** or **********',
        'addNewGuest': 'Add New Guest',
        'home': 'Home',
        'inviteGuest': 'Invite Guests',
        'requests': 'Requests',
        'account': 'Account',
        'about': 'About',
        'logout': 'Logout',
        'updatedsuccessfully': 'Password updated successfully!',
        'incorrectPassword': 'Incorrect password. Please try again.',
        'failedUpdatePassword': 'Failed to update password. Please try again.',
        'updateYourPassword': 'Update Your Password',
        'currentPassword': 'Current Password',
        'confirmPassword': 'Confirm Password',
        'updating': 'Updating...',
        'updatingPassword': 'Update Password',
        'validPassword': 'Password must be at least 6 characters',
        'doNotMatch': 'Passwords do not match',
        'changePassword': 'Change Password',
        'ensurePassword':'Ensure your account is secure by choosing a strong password',
        'guestRequests': 'Guest Requests',
        'sentBy': 'Sent by',
        'rejectionReason': 'Rejection Reason',
        'validRejection': 'Please provide a reason for rejection',
        'savedsuccessfully': 'Guest saved successfully',
        'vaildregistration': 'Please attach a document for group registration',
        'search':'Search',
        'selectPersonFirst':'Select approver person first',
        'send':'Send',
        'isMoreThanOneDays':'Is more than one day?',
        'numberOfDays':'Number of Days',
        'validateNumberOfDays':'Number of days is required',
        'vipRequests':'Vip Guests',
        'success':'Successfully made changes',
        'failed':'Failed to make changes',
        'sentAt':'Sent at',
        'approve_l':'Approve',
        'reject_l':'Reject',
        'verify_device':'Verify device',
        'enter_otp_for_verification':'Enter OTP for device verification',
      };
}
