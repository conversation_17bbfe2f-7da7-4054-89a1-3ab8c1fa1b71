// import 'package:flutter/material.dart';
// import 'package:guest_mobile_app/l10n/app_localizations.dart';

// class CustomDrawer extends StatefulWidget {
//   final Map<String, dynamic> profileData;

//   const CustomDrawer({Key? key, required this.profileData}) : super(key: key);

//   @override
//   _CustomDrawerState createState() => _CustomDrawerState();
// }

// class _CustomDrawerState extends State<CustomDrawer>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _controller;

//   @override
//   void initState() {
//     super.initState();
//     _controller = AnimationController(
//       duration: const Duration(milliseconds: 500),
//       vsync: this,
//     )..forward();
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Drawer(
//       child: Container(
//         decoration: BoxDecoration(
//           gradient: LinearGradient(
//             begin: Alignment.topLeft,
//             end: Alignment.bottomRight,
//             colors: [
//               Theme.of(context).primaryColor.withOpacity(0.9),
//               Theme.of(context).colorScheme.secondary,
//             ],
//           ),
//         ),
//         child: SlideTransition(
//           position: Tween<Offset>(
//             begin: const Offset(-1, 0),
//             end: Offset.zero,
//           ).animate(CurvedAnimation(
//             parent: _controller,
//             curve: Curves.easeOutCubic,
//           )),
//           child: Column(
//             children: [
//               Container(
//                 padding: EdgeInsets.only(
//                   top: MediaQuery.of(context).padding.top + 20,
//                   bottom: 20,
//                 ),
//                 child: Column(
//                   children: [
//                     Hero(
//                       tag: 'profile_image',
//                       child: Container(
//                         padding: const EdgeInsets.all(3),
//                         decoration: BoxDecoration(
//                           shape: BoxShape.circle,
//                           border: Border.all(color: Colors.white, width: 2),
//                         ),
//                         child: CircleAvatar(
//                           radius: 40,
//                           backgroundImage: widget.profileData['avatar'] != null
//                               ? NetworkImage(widget.profileData['avatar'])
//                               : const AssetImage(
//                                       'assets/images/icons8-person.gif')
//                                   as ImageProvider,
//                         ),
//                       ),
//                     ),
//                     const SizedBox(height: 12),
//                     Text(
//                       widget.profileData['fullName'] ?? 'Guest User',
//                       style: const TextStyle(
//                         color: Colors.white,
//                         fontSize: 20,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     Text(
//                       widget.profileData['level'] ?? '<EMAIL>',
//                       style: TextStyle(
//                         color: Colors.white.withOpacity(0.8),
//                         fontSize: 14,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               const Divider(color: Colors.white24),
//               Expanded(
//                 child: ListView(
//                   padding: EdgeInsets.zero,
//                   children: [
//                     _buildAnimatedMenuItem(
//                       icon: Icons.home_rounded,
//                       title: AppLocalizations.of(context)!.home,
//                       onTap: () => Navigator.pushNamed(context, '/home'),
//                       delay: 0.2,
//                     ),
//                     _buildAnimatedMenuItem(
//                       icon: Icons.group_add_rounded,
//                       title: AppLocalizations.of(context)!.inviteGuest,
//                       onTap: () => Navigator.pushNamed(context, '/invites'),
//                       delay: 0.3,
//                     ),
//                     if (widget.profileData['isHead'] == 1)
//                       _buildAnimatedMenuItem(
//                         icon: Icons.pending_actions_rounded,
//                         title: AppLocalizations.of(context)!.requests,
//                         onTap: () => Navigator.pushNamed(context, '/requests'),
//                         delay: 0.4,
//                       ),
//                     _buildAnimatedMenuItem(
//                       icon: Icons.account_circle_rounded,
//                       title: AppLocalizations.of(context)!.account,
//                       onTap: () =>
//                           Navigator.pushNamed(context, '/change-password'),
//                       delay: 0.5,
//                     ),
//                     _buildAnimatedMenuItem(
//                       icon: Icons.info_rounded,
//                       title: AppLocalizations.of(context)!.about,
//                       onTap: () => {},
//                       delay: 0.6,
//                     ),
//                   ],
//                 ),
//               ),
//               const Divider(color: Colors.white24),
//               _buildAnimatedMenuItem(
//                 icon: Icons.logout_rounded,
//                 title: AppLocalizations.of(context)!.logout,
//                 onTap: () => _showLogoutDialog(context),
//                 delay: 0.7,
//                 isLogout: true,
//               ),
//               const SizedBox(height: 20),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildAnimatedMenuItem({
//     required IconData icon,
//     required String title,
//     required VoidCallback onTap,
//     required double delay,
//     bool isLogout = false,
//   }) {
//     return AnimatedBuilder(
//       animation: _controller,
//       builder: (context, child) {
//         final animation = Tween<double>(begin: 0, end: 1).animate(
//           CurvedAnimation(
//             parent: _controller,
//             curve: Interval(delay, delay + 0.2, curve: Curves.easeOut),
//           ),
//         );
//         return FadeTransition(
//           opacity: animation,
//           child: SlideTransition(
//             position: Tween<Offset>(
//               begin: const Offset(-0.5, 0),
//               end: Offset.zero,
//             ).animate(animation),
//             child: child,
//           ),
//         );
//       },
//       child: ListTile(
//         leading: Icon(
//           icon,
//           color: isLogout ? Colors.redAccent : Colors.white,
//           size: 26,
//         ),
//         title: Text(
//           title,
//           style: TextStyle(
//             color: isLogout ? Colors.redAccent : Colors.white,
//             fontSize: 16,
//             fontWeight: FontWeight.w500,
//           ),
//         ),
//         onTap: onTap,
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
//         minLeadingWidth: 20,
//         contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
//       ),
//     );
//   }

//   Future<void> _showLogoutDialog(BuildContext context) async {
//     return showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('Logout'),
//         content: const Text('Are you sure you want to log out?'),
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.pop(context),
//             child: const Text('Cancel'),
//           ),
//           TextButton(
//             onPressed: () {
//               Navigator.pop(context);
//               Navigator.pushReplacementNamed(context, '/login');
//             },
//             child: const Text(
//               'Logout',
//               style: TextStyle(color: Colors.red),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
import 'package:flutter/material.dart';
import 'package:guest_mobile_app/l10n/app_localizations.dart';

class CustomDrawer extends StatefulWidget {
  final Map<String, dynamic> profileData;

  const CustomDrawer({Key? key, required this.profileData}) : super(key: key);

  @override
  _CustomDrawerState createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 700),
      vsync: this,
    )..forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF2B2D3E),
              Color(0xFF1F2634),
            ],
          ),
        ),
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(-1, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _controller,
            curve: Curves.easeOutCubic,
          )),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top + 20,
                  bottom: 20,
                ),
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black26,
                            blurRadius: 15,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Hero(
                        tag: 'profile_image',
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                Colors.blue.shade300,
                                Colors.purple.shade300
                              ],
                            ),
                          ),
                          child: CircleAvatar(
                            radius: 45,
                            backgroundColor: Colors.white,
                            child: CircleAvatar(
                              radius: 42,
                              backgroundImage: widget.profileData['avatar'] !=
                                      null
                                  ? NetworkImage(widget.profileData['avatar'])
                                  : const AssetImage(
                                          'assets/images/icons8-person.gif')
                                      as ImageProvider,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 15),
                    Text(
                      widget.profileData['fullName'] ?? 'Guest User',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white12,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Text(
                        widget.profileData['level'] ?? '<EMAIL>',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Divider(color: Colors.white24, thickness: 1),
              ),
              Expanded(
                child: ListView(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  children: [
                    _buildAnimatedMenuItem(
                      icon: Icons.home_rounded,
                      title: AppLocalizations.of(context)!.home,
                      onTap: () => Navigator.pushNamed(context, '/home'),
                      delay: 0.2,
                    ),
                    _buildAnimatedMenuItem(
                      icon: Icons.group_add_rounded,
                      title: AppLocalizations.of(context)!.inviteGuest,
                      onTap: () => Navigator.pushNamed(context, '/invites'),
                      delay: 0.3,
                    ),
                    if (widget.profileData['isHead'] == 1)
                      _buildAnimatedMenuItem(
                        icon: Icons.pending_actions_rounded,
                        title: AppLocalizations.of(context)!.requests,
                        onTap: () => Navigator.pushNamed(context, '/requests'),
                        delay: 0.4,
                      ),
                        if (widget.profileData['isHead'] == 1 && widget.profileData['user_level'] == 6 || widget.profileData['isHead'] == 1 && widget.profileData['user_level'] == 1)
                      _buildAnimatedMenuItem(
                        icon: Icons.pending_outlined,
                        title: AppLocalizations.of(context)!.vipRequests,
                        onTap: () => Navigator.pushNamed(context, '/vipRequests'),
                        delay: 0.4,
                      ),
                    _buildAnimatedMenuItem(
                      icon: Icons.account_circle_rounded,
                      title: AppLocalizations.of(context)!.account,
                      onTap: () =>
                          Navigator.pushNamed(context, '/change-password'),
                      delay: 0.5,
                    ),
                    _buildAnimatedMenuItem(
                      icon: Icons.info_rounded,
                      title: AppLocalizations.of(context)!.about,
                      onTap: () => {},
                      delay: 0.6,
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Divider(color: Colors.white24, thickness: 1),
              ),
              _buildAnimatedMenuItem(
                icon: Icons.logout_rounded,
                title: AppLocalizations.of(context)!.logout,
                onTap: () => _showLogoutDialog(context),
                delay: 0.7,
                isLogout: true,
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    required double delay,
    bool isLogout = false,
  }) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final animation = Tween<double>(begin: 0, end: 1).animate(
          CurvedAnimation(
            parent: _controller,
            curve: Interval(delay, delay + 0.2, curve: Curves.easeOut),
          ),
        );
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(-0.5, 0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: isLogout
              ? Colors.red.withOpacity(0.1)
              : Colors.white.withOpacity(0.1),
        ),
        child: ListTile(
          leading: Icon(
            icon,
            color: isLogout ? Colors.red.shade300 : Colors.white,
            size: 26,
          ),
          title: Text(
            title,
            style: TextStyle(
              color: isLogout ? Colors.red.shade300 : Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          onTap: onTap,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          minLeadingWidth: 20,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        ),
      ),
    );
  }

  Future<void> _showLogoutDialog(BuildContext context) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Color(0xFF2C3E50),
        title: Text('Logout', style: TextStyle(color: Colors.white)),
        content: Text('Are you sure you want to log out?',
            style: TextStyle(color: Colors.white70)),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel', style: TextStyle(color: Colors.white70)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushReplacementNamed(context, '/login');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade400,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: Text('Logout'),
          ),
        ],
      ),
    );
  }
}
